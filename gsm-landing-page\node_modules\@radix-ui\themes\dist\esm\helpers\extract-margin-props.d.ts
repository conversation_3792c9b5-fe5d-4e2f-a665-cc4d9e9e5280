import type { MarginProps } from '../props/margin.props.js';
export declare function extractMarginProps<T extends MarginProps>(props: T): {
    m: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    mx: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    my: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    mt: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    mr: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    mb: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    ml: import("../props/prop-def.js").Responsive<import("../props/prop-def.js").Union<string, "0" | "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "-1" | "-2" | "-3" | "-4" | "-5" | "-6" | "-7" | "-8" | "-9">> | undefined;
    rest: Omit<T, "m" | "ml" | "mr" | "mt" | "mb" | "mx" | "my">;
};
//# sourceMappingURL=extract-margin-props.d.ts.map