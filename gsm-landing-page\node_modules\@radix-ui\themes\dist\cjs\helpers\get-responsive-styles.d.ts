import type { Responsive, Union } from '../props/prop-def.js';
interface GetResponsiveStylesOptions {
    className: string;
    customProperties: `--${string}`[];
    value: Responsive<Union> | Responsive<string> | undefined;
    propValues: string[] | readonly string[];
    parseValue?: (value: string) => string | undefined;
}
declare function getResponsiveStyles({ className, customProperties, ...args }: GetResponsiveStylesOptions): readonly [string | undefined, Record<string, string | undefined> | undefined];
interface GetResponsiveClassNamesOptions {
    allowArbitraryValues?: boolean;
    className: string;
    value: Responsive<Union> | Responsive<string> | undefined;
    propValues: string[] | readonly string[];
    parseValue?: (value: string) => string | undefined;
}
declare function getResponsiveClassNames({ allowArbitraryValues, value, className, propValues, parseValue, }: GetResponsiveClassNamesOptions): string | undefined;
interface GetResponsiveCustomPropertiesOptions {
    customProperties: `--${string}`[];
    value: Responsive<Union> | Responsive<string> | undefined;
    propValues: string[] | readonly string[];
    parseValue?: (value: string) => string | undefined;
}
declare function getResponsiveCustomProperties({ customProperties, value, propValues, parseValue, }: GetResponsiveCustomPropertiesOptions): Record<string, string | undefined> | undefined;
export { getResponsiveStyles, getResponsiveCustomProperties, getResponsiveClassNames };
//# sourceMappingURL=get-responsive-styles.d.ts.map