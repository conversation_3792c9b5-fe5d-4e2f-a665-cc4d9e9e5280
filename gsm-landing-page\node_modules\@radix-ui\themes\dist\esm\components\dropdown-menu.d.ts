import * as React from 'react';
import { DropdownMenu as DropdownMenuPrimitive } from 'radix-ui';
import { dropdownMenuContentPropDefs, dropdownMenuItemPropDefs, dropdownMenuCheckboxItemPropDefs, dropdownMenuRadioItemPropDefs } from './dropdown-menu.props.js';
import { ChevronDownIcon } from './icons.js';
import type { IconProps } from './icons.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
interface DropdownMenuRootProps extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Root> {
}
declare const DropdownMenuRoot: React.FC<DropdownMenuRootProps>;
interface DropdownMenuTriggerProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Trigger, RemovedProps> {
}
declare const DropdownMenuTrigger: React.ForwardRefExoticComponent<DropdownMenuTriggerProps & React.RefAttributes<HTMLButtonElement>>;
type DropdownMenuContentOwnProps = GetPropDefTypes<typeof dropdownMenuContentPropDefs>;
type DropdownMenuContentContextValue = DropdownMenuContentOwnProps;
interface DropdownMenuContentProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Content, RemovedProps>, DropdownMenuContentContextValue {
    container?: React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>['container'];
}
declare const DropdownMenuContent: React.ForwardRefExoticComponent<DropdownMenuContentProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuLabelProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Label, RemovedProps> {
}
declare const DropdownMenuLabel: React.ForwardRefExoticComponent<DropdownMenuLabelProps & React.RefAttributes<HTMLDivElement>>;
type DropdownMenuItemOwnProps = GetPropDefTypes<typeof dropdownMenuItemPropDefs>;
interface DropdownMenuItemProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Item, RemovedProps>, DropdownMenuItemOwnProps {
}
declare const DropdownMenuItem: React.ForwardRefExoticComponent<DropdownMenuItemProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuGroupProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Group, RemovedProps> {
}
declare const DropdownMenuGroup: React.ForwardRefExoticComponent<DropdownMenuGroupProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuRadioGroupProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.RadioGroup, RemovedProps> {
}
declare const DropdownMenuRadioGroup: React.ForwardRefExoticComponent<DropdownMenuRadioGroupProps & React.RefAttributes<HTMLDivElement>>;
type DropdownMenuRadioItemOwnProps = GetPropDefTypes<typeof dropdownMenuRadioItemPropDefs>;
interface DropdownMenuRadioItemProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.RadioItem, RemovedProps>, DropdownMenuRadioItemOwnProps {
}
declare const DropdownMenuRadioItem: React.ForwardRefExoticComponent<DropdownMenuRadioItemProps & React.RefAttributes<HTMLDivElement>>;
type DropdownMenuCheckboxItemOwnProps = GetPropDefTypes<typeof dropdownMenuCheckboxItemPropDefs>;
interface DropdownMenuCheckboxItemProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.CheckboxItem, RemovedProps>, DropdownMenuCheckboxItemOwnProps {
}
declare const DropdownMenuCheckboxItem: React.ForwardRefExoticComponent<DropdownMenuCheckboxItemProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuSubProps extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Sub> {
}
declare const DropdownMenuSub: React.FC<DropdownMenuSubProps>;
interface DropdownMenuSubTriggerProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.SubTrigger, RemovedProps> {
}
declare const DropdownMenuSubTrigger: React.ForwardRefExoticComponent<DropdownMenuSubTriggerProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuSubContentProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.SubContent, RemovedProps> {
    container?: React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>['container'];
}
declare const DropdownMenuSubContent: React.ForwardRefExoticComponent<DropdownMenuSubContentProps & React.RefAttributes<HTMLDivElement>>;
interface DropdownMenuSeparatorProps extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Separator, RemovedProps> {
}
declare const DropdownMenuSeparator: React.ForwardRefExoticComponent<DropdownMenuSeparatorProps & React.RefAttributes<HTMLDivElement>>;
export { DropdownMenuRoot as Root, DropdownMenuTrigger as Trigger, ChevronDownIcon as TriggerIcon, DropdownMenuContent as Content, DropdownMenuLabel as Label, DropdownMenuItem as Item, DropdownMenuGroup as Group, DropdownMenuRadioGroup as RadioGroup, DropdownMenuRadioItem as RadioItem, DropdownMenuCheckboxItem as CheckboxItem, DropdownMenuSub as Sub, DropdownMenuSubTrigger as SubTrigger, DropdownMenuSubContent as SubContent, DropdownMenuSeparator as Separator, };
export type { DropdownMenuRootProps as RootProps, DropdownMenuTriggerProps as TriggerProps, IconProps as TriggerIconProps, DropdownMenuContentProps as ContentProps, DropdownMenuLabelProps as LabelProps, DropdownMenuItemProps as ItemProps, DropdownMenuGroupProps as GroupProps, DropdownMenuRadioGroupProps as RadioGroupProps, DropdownMenuRadioItemProps as RadioItemProps, DropdownMenuCheckboxItemProps as CheckboxItemProps, DropdownMenuSubProps as SubProps, DropdownMenuSubTriggerProps as SubTriggerProps, DropdownMenuSubContentProps as SubContentProps, DropdownMenuSeparatorProps as SeparatorProps, };
//# sourceMappingURL=dropdown-menu.d.ts.map