import * as React from 'react';
import { badgePropDefs } from './badge.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type BadgeOwnProps = GetPropDefTypes<typeof badgePropDefs>;
interface BadgeProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, BadgeOwnProps {
}
declare const Badge: React.ForwardRefExoticComponent<BadgeProps & React.RefAttributes<HTMLSpanElement>>;
export { Badge };
export type { BadgeProps };
//# sourceMappingURL=badge.d.ts.map