import * as React from 'react';
import { Switch as SwitchPrimitive } from 'radix-ui';
import { switchPropDefs } from './switch.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
import type { ComponentPropsWithout } from '../helpers/component-props.js';
type SwitchOwnProps = GetPropDefTypes<typeof switchPropDefs>;
interface SwitchProps extends ComponentPropsWithout<typeof SwitchPrimitive.Root, 'asChild' | 'color' | 'defaultValue' | 'children'>, MarginProps, SwitchOwnProps {
}
declare const Switch: React.ForwardRefExoticComponent<SwitchProps & React.RefAttributes<HTMLButtonElement>>;
export { Switch };
export type { SwitchProps };
//# sourceMappingURL=switch.d.ts.map