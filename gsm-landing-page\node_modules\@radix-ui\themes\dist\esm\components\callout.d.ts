import * as React from 'react';
import { Text } from './text.js';
import { calloutRootPropDefs } from './callout.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps, ComponentPropsAs } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type CalloutRootOwnProps = GetPropDefTypes<typeof calloutRootPropDefs>;
interface CalloutRootProps extends ComponentPropsWithout<'div', RemovedProps>, MarginProps, CalloutRootOwnProps {
}
declare const CalloutRoot: React.ForwardRefExoticComponent<CalloutRootProps & React.RefAttributes<HTMLDivElement>>;
interface CalloutIconProps extends ComponentPropsWithout<'div', RemovedProps> {
}
declare const CalloutIcon: React.ForwardRefExoticComponent<CalloutIconProps & React.RefAttributes<HTMLDivElement>>;
type CalloutTextProps = ComponentPropsAs<typeof Text, 'p'>;
declare const CalloutText: React.ForwardRefExoticComponent<CalloutTextProps & React.RefAttributes<HTMLParagraphElement>>;
export { CalloutRoot as Root, CalloutIcon as Icon, CalloutText as Text };
export type { CalloutRootProps as RootProps, CalloutIconProps as IconProps, CalloutTextProps as TextProps, };
//# sourceMappingURL=callout.d.ts.map