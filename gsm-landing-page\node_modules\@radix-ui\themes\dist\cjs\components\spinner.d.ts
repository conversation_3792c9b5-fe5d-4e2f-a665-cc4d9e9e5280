import * as React from 'react';
import { spinnerPropDefs } from './spinner.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type SpinnerOwnProps = GetPropDefTypes<typeof spinnerPropDefs>;
interface SpinnerProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, SpinnerOwnProps {
}
declare const Spinner: React.ForwardRefExoticComponent<SpinnerProps & React.RefAttributes<HTMLSpanElement>>;
export { Spinner };
export type { SpinnerProps };
//# sourceMappingURL=spinner.d.ts.map