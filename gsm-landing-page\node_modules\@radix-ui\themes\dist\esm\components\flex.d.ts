import * as React from 'react';
import type { FlexOwnProps } from './flex.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { LayoutProps } from '../props/layout.props.js';
import type { MarginProps } from '../props/margin.props.js';
interface CommonFlexProps extends MarginProps, LayoutProps, FlexOwnProps {
}
type FlexDivProps = {
    as?: 'div';
} & ComponentPropsWithout<'div', RemovedProps>;
type FlexSpanProps = {
    as: 'span';
} & ComponentPropsWithout<'span', RemovedProps>;
type FlexProps = CommonFlexProps & (FlexSpanProps | FlexDivProps);
declare const Flex: React.ForwardRefExoticComponent<FlexProps & React.RefAttributes<HTMLDivElement>>;
export { Flex };
export type { FlexProps };
//# sourceMappingURL=flex.d.ts.map