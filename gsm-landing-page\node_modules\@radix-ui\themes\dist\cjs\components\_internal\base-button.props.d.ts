declare const baseButtonPropDefs: {
    loading: {
        type: "boolean";
        className: string;
        default: false;
    };
    radius: {
        type: "enum";
        values: readonly ["none", "small", "medium", "large", "full"];
        default: undefined;
    };
    highContrast: {
        type: "boolean";
        className: string;
        default: undefined;
    };
    color: {
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
        default: "gray" | "gold" | "bronze" | "brown" | "yellow" | "amber" | "orange" | "tomato" | "red" | "ruby" | "crimson" | "pink" | "plum" | "purple" | "violet" | "iris" | "indigo" | "blue" | "cyan" | "teal" | "jade" | "green" | "grass" | "lime" | "mint" | "sky";
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4"];
        default: "2";
        responsive: true;
    };
    variant: {
        type: "enum";
        className: string;
        values: readonly ["classic", "solid", "soft", "surface", "outline", "ghost"];
        default: "solid";
    };
    asChild: {
        type: "boolean";
    };
};
export { baseButtonPropDefs };
//# sourceMappingURL=base-button.props.d.ts.map