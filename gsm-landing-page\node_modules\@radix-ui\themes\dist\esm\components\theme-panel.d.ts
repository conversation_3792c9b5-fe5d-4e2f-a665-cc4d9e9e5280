import * as React from 'react';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface ThemePanelProps extends Omit<ThemePanelImplProps, keyof ThemePanelImplPrivateProps> {
    defaultOpen?: boolean;
}
declare const ThemePanel: React.ForwardRefExoticComponent<ThemePanelProps & React.RefAttributes<HTMLDivElement>>;
interface ThemePanelImplPrivateProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}
interface ThemePanelImplProps extends ComponentPropsWithout<'div', RemovedProps>, ThemePanelImplPrivateProps {
    onAppearanceChange?: (value: 'light' | 'dark') => void;
}
export { ThemePanel };
export type { ThemePanelProps };
//# sourceMappingURL=theme-panel.d.ts.map