import * as React from 'react';
import { codePropDefs } from './code.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type CodeOwnProps = GetPropDefTypes<typeof codePropDefs>;
interface CodeProps extends ComponentPropsWithout<'code', RemovedProps>, MarginProps, CodeOwnProps {
}
declare const Code: React.ForwardRefExoticComponent<CodeProps & React.RefAttributes<HTMLElement>>;
export { Code };
export type { CodeProps };
//# sourceMappingURL=code.d.ts.map