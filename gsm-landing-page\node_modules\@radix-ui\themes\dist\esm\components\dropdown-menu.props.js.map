{"version": 3, "sources": ["../../../src/components/dropdown-menu.props.tsx"], "sourcesContent": ["export {\n  baseMenuContentPropDefs as dropdownMenuContentPropDefs,\n  baseMenuItemPropDefs as dropdownMenuItemPropDefs,\n  baseMenuCheckboxItemPropDefs as dropdownMenuCheckboxItemPropDefs,\n  baseMenuRadioItemPropDefs as dropdownMenuRadioItemPropDefs,\n} from './_internal/base-menu.props.js';\n"], "mappings": "AAAA,OAC6B,2BAA3BA,EACwB,wBAAxBC,EACgC,gCAAhCC,EAC6B,6BAA7BC,MACK", "names": ["baseMenuContentPropDefs", "baseMenuItemPropDefs", "baseMenuCheckboxItemPropDefs", "baseMenuRadioItemPropDefs"]}