{"version": 3, "sources": ["../../../src/props/width.props.ts"], "sourcesContent": ["import type { GetPropDefTypes, PropDef } from './prop-def.js';\n\nconst widthPropDefs = {\n  /**\n   * Sets the CSS **width** property.\n   * Supports CSS strings and responsive objects.\n   *\n   * @example\n   * width=\"100px\"\n   * width={{ md: '100vw', xl: '1400px' }}\n   *\n   * @link\n   * https://developer.mozilla.org/en-US/docs/Web/CSS/width\n   */\n  width: {\n    type: 'string',\n    className: 'rt-r-w',\n    customProperties: ['--width'],\n    responsive: true,\n  },\n  /**\n   * Sets the CSS **min-width** property.\n   * Supports CSS strings and responsive objects.\n   *\n   * @example\n   * minWidth=\"100px\"\n   * minWidth={{ md: '100vw', xl: '1400px' }}\n   *\n   * @link\n   * https://developer.mozilla.org/en-US/docs/Web/CSS/min-width\n   */\n  minWidth: {\n    type: 'string',\n    className: 'rt-r-min-w',\n    customProperties: ['--min-width'],\n    responsive: true,\n  },\n  /**\n   * Sets the CSS **max-width** property.\n   * Supports CSS strings and responsive objects.\n   *\n   * @example\n   * maxWidth=\"100px\"\n   * maxWidth={{ md: '100vw', xl: '1400px' }}\n   *\n   * @link\n   * https://developer.mozilla.org/en-US/docs/Web/CSS/max-width\n   */\n  maxWidth: {\n    type: 'string',\n    className: 'rt-r-max-w',\n    customProperties: ['--max-width'],\n    responsive: true,\n  },\n} satisfies {\n  width: PropDef<string>;\n  minWidth: PropDef<string>;\n  maxWidth: PropDef<string>;\n};\n\ntype WidthProps = GetPropDefTypes<typeof widthPropDefs>;\n\nexport { widthPropDefs };\nexport type { WidthProps };\n"], "mappings": "AAEA,MAAMA,EAAgB,CAYpB,MAAO,CACL,KAAM,SACN,UAAW,SACX,iBAAkB,CAAC,SAAS,EAC5B,WAAY,EACd,EAYA,SAAU,CACR,KAAM,SACN,UAAW,aACX,iBAAkB,CAAC,aAAa,EAChC,WAAY,EACd,EAYA,SAAU,CACR,KAAM,SACN,UAAW,aACX,iBAAkB,CAAC,aAAa,EAChC,WAAY,EACd,CACF", "names": ["widthPropDefs"]}