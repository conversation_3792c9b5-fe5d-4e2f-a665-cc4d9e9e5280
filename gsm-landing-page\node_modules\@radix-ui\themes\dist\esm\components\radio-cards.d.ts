import * as React from 'react';
import { RadioGroup as RadioGroupPrimitive } from 'radix-ui';
import { radioCardsRootPropDefs } from './radio-cards.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type RadioCardsRootOwnProps = GetPropDefTypes<typeof radioCardsRootPropDefs>;
interface RadioCardsRootProps extends ComponentPropsWithout<typeof RadioGroupPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>, MarginProps, RadioCardsRootOwnProps {
}
declare const RadioCardsRoot: React.ForwardRefExoticComponent<RadioCardsRootProps & React.RefAttributes<HTMLDivElement>>;
interface RadioCardsItemProps extends ComponentPropsWithout<typeof RadioGroupPrimitive.Item, RemovedProps>, MarginProps {
}
declare const RadioCardsItem: React.ForwardRefExoticComponent<RadioCardsItemProps & React.RefAttributes<HTMLButtonElement>>;
export { RadioCardsRoot as Root, RadioCardsItem as Item };
export type { RadioCardsRootProps as RootProps, RadioCardsItemProps as ItemProps };
//# sourceMappingURL=radio-cards.d.ts.map