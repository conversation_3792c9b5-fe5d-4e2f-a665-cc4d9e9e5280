<template>
  <div class="landing-page">
    <!-- 导航栏 -->
    <header class="header">
      <nav class="nav-container">
        <div class="nav-brand">
          <img src="/logo.svg" alt="平台Logo" class="logo" />
          <h1 class="brand-title">地理信息安全监测平台</h1>
        </div>
        <div class="nav-actions">
          <button
            class="theme-switch"
            type="button"
            role="switch"
            :title="isDark ? 'Switch to light theme' : 'Switch to dark theme'"
            :aria-checked="isDark"
            @click="toggleTheme"
          >
            <span class="switch-track">
              <span class="switch-thumb">
                <span class="switch-icon">
                  <span class="sun-icon" :class="{ active: !isDark }">☀️</span>
                  <span class="moon-icon" :class="{ active: isDark }">🌙</span>
                </span>
              </span>
            </span>
          </button>
          <button class="btn btn-outline" @click="$router.push('/login/government')">
            政府端登录
          </button>
          <button class="btn btn-primary" @click="$router.push('/login/enterprise')">
            企业端登录
          </button>
        </div>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title">
              智能网联汽车时空数据安全监测
            </h1>
            <p class="hero-subtitle">
              构建"国家-属地-企业-终端"四级分布式监管架构，保障时空数据全生命周期安全
            </p>
            <div class="hero-stats">
              <div class="stat-item">
                <span class="stat-number">50万+</span>
                <span class="stat-label">接入车辆总数</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">30+</span>
                <span class="stat-label">服务企业数量</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">4级</span>
                <span class="stat-label">监管架构</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">7×24</span>
                <span class="stat-label">实时监控</span>
              </div>
            </div>
            <div class="hero-actions">
              <button class="btn btn-primary btn-large" @click="$router.push('/login/enterprise')">
                立即接入
              </button>
              <button class="btn btn-outline btn-large" @click="scrollToSection('features')">
                了解更多
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 平台特色功能 -->
      <section id="features" class="features-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">平台核心能力</h2>
            <p class="section-subtitle">
              基于先进技术架构，提供全方位数据安全监管服务
            </p>
          </div>
          <div class="features-grid">
            <div v-for="feature in features" :key="feature.id" class="feature-card">
              <div class="feature-icon">{{ feature.icon }}</div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 运营数据展示 -->
      <section class="stats-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">平台运营数据</h2>
          </div>
          <div class="stats-grid">
            <div v-for="stat in operationalStats" :key="stat.id" class="stat-card">
              <div class="stat-content">
                <span class="stat-value">{{ stat.value }}</span>
                <h4 class="stat-title">{{ stat.title }}</h4>
                <p class="stat-desc">{{ stat.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务企业展示 -->
      <section class="enterprises-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">服务企业</h2>
            <p class="section-subtitle">
              为行业领军企业提供专业的数据安全监测服务
            </p>
          </div>
          <div class="enterprises-grid">
            <div v-for="enterprise in enterprises" :key="enterprise.id" class="enterprise-item">
              <div class="enterprise-logo">{{ enterprise.name.charAt(0) }}</div>
              <span class="enterprise-name">{{ enterprise.name }}</span>
              <span class="enterprise-badge" :class="getEnterpriseTypeClass(enterprise.type)">
                {{ enterprise.type }}
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 政策动态 -->
      <section class="news-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">政策动态</h2>
          </div>
          <div class="news-grid">
            <div v-for="news in newsItems" :key="news.id" class="news-card">
              <div class="news-meta">
                <span class="news-badge">{{ news.category }}</span>
                <span class="news-date">{{ news.date }}</span>
              </div>
              <h3 class="news-title">{{ news.title }}</h3>
              <p class="news-summary">{{ news.summary }}</p>
              <button class="btn btn-ghost btn-small news-link">
                查看详情 →
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4 class="footer-title">地理信息安全监测平台</h4>
            <p class="footer-desc">构建智能网联汽车时空数据安全监管体系</p>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">快速链接</h4>
            <div class="footer-links">
              <a href="#features">平台功能</a>
              <a href="#stats">运营数据</a>
              <a href="#news">政策动态</a>
            </div>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">联系我们</h4>
            <p class="footer-contact">技术支持：<EMAIL></p>
            <p class="footer-contact">监管咨询：<EMAIL></p>
          </div>
        </div>
        <div class="footer-bottom">
          <p class="footer-copyright">© 2025 地理信息安全监测平台. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const getEnterpriseTypeClass = (type: string) => {
  switch (type) {
    case '汽车企业': return 'badge-blue'
    case '地图服务商': return 'badge-green'
    case '智驾方案提供商': return 'badge-orange'
    case '平台运营商': return 'badge-purple'
    default: return 'badge-gray'
  }
}

const features = [
  {
    id: 1,
    icon: '🗺️',
    title: '地理围栏监控',
    description: '动态展示地理围栏范围，实时标记车端和云端分布节点，支持风险热力图展示'
  },
  {
    id: 2,
    icon: '📊',
    title: '分级风险预警',
    description: '车端和云端风险分级显示，高风险闪烁弹窗、中风险呼吸灯提醒、低风险汇总报告'
  },
  {
    id: 3,
    icon: '📋',
    title: '备案审核管理',
    description: '企业信息、测绘资质、车辆信息、数据处理活动全流程备案审核管理'
  },
  {
    id: 4,
    icon: '🔍',
    title: '应急溯源追踪',
    description: '车端和云端数据处理活动全链路溯源，支持责任认定和事件调查'
  },
  {
    id: 5,
    icon: '🔗',
    title: '区块链存证',
    description: '审批记录、风险处置、检查任务等关键信息上链存证，防止篡改'
  },
  {
    id: 6,
    icon: '👥',
    title: '多端协同管理',
    description: '政府端监管、企业端自律，支持多监管员协同和权限精细化管理'
  }
]

const operationalStats = [
  {
    id: 1,
    value: '50万+',
    title: '接入车辆总数',
    description: '覆盖M类、N类、O类及低速无人驾驶装备'
  },
  {
    id: 2,
    value: '30+',
    title: '服务企业数量',
    description: '汽车企业、地图服务商、智驾方案提供商、平台运营商'
  },
  {
    id: 3,
    value: '3分钟',
    title: '实时数据更新',
    description: '车端和云端处理活动信息实时上报'
  },
  {
    id: 4,
    value: '11个',
    title: '数据处理环节',
    description: '收集、存储、传输、加工、提供、公开、销毁等全覆盖'
  },
  {
    id: 5,
    value: '4级',
    title: '风险预警等级',
    description: '特别重大、重大、较大、一般风险分级管理'
  },
  {
    id: 6,
    value: 'PB级',
    title: '数据存储能力',
    description: '支持海量时空数据安全存储和快速检索'
  }
]

const enterprises = [
  { id: 1, name: '北京汽车集团', type: '汽车企业' },
  { id: 2, name: '吉利汽车研究院', type: '汽车企业' },
  { id: 3, name: '上海汽车集团', type: '汽车企业' },
  { id: 4, name: '长城汽车', type: '汽车企业' },
  { id: 5, name: '东风商用车', type: '汽车企业' },
  { id: 6, name: '郑州宇通客车', type: '汽车企业' },
  { id: 7, name: '蔚马汽车技术', type: '汽车企业' },
  { id: 8, name: '阿里巴巴网络技术', type: '地图服务商' },
  { id: 9, name: '华为技术', type: '智驾方案提供商' },
  { id: 10, name: '杭州飞步科技', type: '智驾方案提供商' },
  { id: 11, name: '百度地图', type: '地图服务商' },
  { id: 12, name: '高德地图', type: '地图服务商' }
]

const newsItems = [
  {
    id: 1,
    category: '政策法规',
    date: '2025-01-15',
    title: '智能网联汽车时空数据安全监测平台通信协议规范发布',
    summary: '规范了企业平台与政府监测平台之间的通信协议，包括TCP/IP网络控制协议、数据包结构、传输方式及安全机制等内容。'
  },
  {
    id: 2,
    category: '技术标准',
    date: '2025-01-10',
    title: '智能网联汽车时空数据安全风险项（类别）清单征求意见稿',
    summary: '涵盖智能网联汽车时空数据的定义、分类分级及风险识别等重要内容，为风险预警提供标准依据。'
  },
  {
    id: 3,
    category: '行业动态',
    date: '2025-01-08',
    title: '四级分布式监管架构正式启动运行',
    summary: '国家-属地-企业-终端四级监管体系全面投入使用，实现对智能网联汽车时空数据的全覆盖监管。'
  }
]

onMounted(() => {
  // 初始化主题
  document.documentElement.setAttribute('data-theme', 'light')
})
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: var(--background-color);
  width: 100%;
}

/* 通用容器 - 全屏宽度显示 */
.container {
  width: 100%;
  margin: 0;
  padding: 0 2rem;
  box-sizing: border-box;
}

/* Section内的容器需要居中对齐 */
.hero-section .container,
.features-section .container,
.stats-section .container,
.enterprises-section .container,
.news-section .container,
.footer .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-outline {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.btn-ghost {
  color: var(--text-color-regular);
}

.btn-ghost:hover {
  background: var(--gray-extra-light);
  color: var(--text-color);
}

.btn-large {
  padding: 0.75rem 2rem;
  font-size: 1rem;
}

.btn-small {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

/* 徽章样式 */
.enterprise-badge, .news-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-blue {
  background: #e3f2fd;
  color: #1976d2;
}

.badge-green {
  background: #e8f5e8;
  color: #2e7d32;
}

.badge-orange {
  background: #fff3e0;
  color: #f57c00;
}

.badge-purple {
  background: #f3e5f5;
  color: #7b1fa2;
}

.badge-gray {
  background: var(--gray-extra-light);
  color: var(--text-color-secondary);
}

.news-badge {
  background: var(--primary-color);
  color: white;
}

/* 导航栏样式 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(8px);
  width: 100%;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  width: 100%;
  box-sizing: border-box;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  width: 40px;
  height: 40px;
}

.brand-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 主题切换按钮样式 - 复刻Radix Vue */
.theme-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  width: 44px;
  height: 22px;
  border: 1px solid var(--border-color);
  border-radius: 11px;
  background: var(--gray-extra-light);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  padding: 1px;
}

.theme-switch:hover {
  border-color: var(--primary-color);
  background: var(--background-color);
}

.theme-switch:focus-visible {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15);
}

.switch-track {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 11px;
  overflow: hidden;
}

.switch-thumb {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .switch-thumb {
  transform: translateX(22px);
  background: white;
}

.switch-icon {
  position: relative;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sun-icon, .moon-icon {
  position: absolute;
  font-size: 11px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: scale(0.6) rotate(180deg);
}

.sun-icon.active, .moon-icon.active {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

/* 深色主题下的样式调整 */
[data-theme="dark"] .theme-switch {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

[data-theme="dark"] .theme-switch:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* 英雄区域样式 */
.hero-section {
  padding: 8rem 0;
  text-align: center;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(64, 158, 255, 0.08) 100%),
              url('/hero-bg-new.jpg') center/cover;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
  color: var(--text-color);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.92);
  z-index: 1;
  transition: background 0.3s ease;
}

[data-theme="dark"] .hero-section::before {
  background: rgba(17, 17, 17, 0.85);
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.hero-content {
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-active) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: 1.35rem;
  margin-bottom: 3rem;
  color: var(--text-color-regular);
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  min-width: 120px;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* 通用section样式 */
.features-section, .enterprises-section {
  padding: 6rem 0;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.stats-section, .news-section {
  padding: 6rem 0;
  background: var(--gray-extra-light);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  margin-top: 1rem;
  color: var(--text-color-secondary);
  line-height: 1.6;
}

/* 功能特色区域 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: none;
}

.feature-card {
  padding: 2rem;
  text-align: center;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-color-regular);
  line-height: 1.6;
}

/* 运营数据区域 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: none;
}

.stat-card {
  padding: 2rem;
  text-align: center;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.stat-desc {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

/* 服务企业区域 */
.enterprises-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
  max-width: none;
}

.enterprise-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.enterprise-item:hover {
  border-color: var(--primary-color);
  background: var(--gray-extra-light);
  transform: translateY(-2px);
}

.enterprise-logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.enterprise-name {
  font-weight: 500;
  color: var(--text-color);
  text-align: center;
}

/* 新闻动态区域 */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  max-width: none;
}

.news-card {
  padding: 2rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.news-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.news-date {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.news-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.news-summary {
  margin-bottom: 1.5rem;
  color: var(--text-color-regular);
  line-height: 1.6;
}

.news-link {
  align-self: flex-start;
}

/* 页脚样式 */
.footer {
  padding: 4rem 0 2rem;
  background: var(--text-color);
  color: var(--background-color);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--background-color);
}

.footer-desc, .footer-contact {
  font-size: 0.875rem;
  color: var(--gray-light);
  margin-bottom: 0.5rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  font-size: 0.875rem;
  color: var(--gray-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--gray-color);
}

.footer-copyright {
  font-size: 0.875rem;
  color: var(--gray-light);
  margin: 0;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .container {
    padding: 0 3rem;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
  }

  .enterprises-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
  }

  .news-grid {
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 3rem;
  }
}

@media (min-width: 1920px) {
  .container {
    padding: 0 4rem;
  }

  .hero-content {
    max-width: 1200px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .nav-container {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .nav-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .enterprises-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .news-grid {
    grid-template-columns: 1fr;
  }

  .hero-section,
  .features-section,
  .stats-section,
  .enterprises-section,
  .news-section {
    padding: 4rem 0;
  }
}
</style>
