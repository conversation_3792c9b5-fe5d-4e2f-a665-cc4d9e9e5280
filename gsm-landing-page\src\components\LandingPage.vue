<template>
  <div class="landing-page">
    <!-- 导航栏 -->
    <header class="header">
      <nav class="nav-container">
        <div class="nav-brand">
          <img src="/logo.svg" alt="平台Logo" class="logo" />
          <h1 class="brand-title">地理信息安全监测平台</h1>
        </div>
        <div class="nav-actions">
          <Button variant="ghost" @click="toggleTheme">
            {{ isDark ? '🌞' : '🌙' }}
          </Button>
          <Button variant="outline" @click="$router.push('/login/government')">
            政府端登录
          </Button>
          <Button @click="$router.push('/login/enterprise')">
            企业端登录
          </Button>
        </div>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <Container size="4">
          <div class="hero-content">
            <Heading size="9" class="hero-title">
              智能网联汽车时空数据安全监测
            </Heading>
            <Text size="6" class="hero-subtitle">
              构建"国家-属地-企业-终端"四级分布式监管架构，保障时空数据全生命周期安全
            </Text>
            <div class="hero-stats">
              <div class="stat-item">
                <Text size="8" weight="bold" class="stat-number">50万+</Text>
                <Text size="3" class="stat-label">接入车辆总数</Text>
              </div>
              <div class="stat-item">
                <Text size="8" weight="bold" class="stat-number">30+</Text>
                <Text size="3" class="stat-label">服务企业数量</Text>
              </div>
              <div class="stat-item">
                <Text size="8" weight="bold" class="stat-number">4级</Text>
                <Text size="3" class="stat-label">监管架构</Text>
              </div>
              <div class="stat-item">
                <Text size="8" weight="bold" class="stat-number">7×24</Text>
                <Text size="3" class="stat-label">实时监控</Text>
              </div>
            </div>
            <div class="hero-actions">
              <Button size="4" @click="$router.push('/login/enterprise')">
                立即接入
              </Button>
              <Button size="4" variant="outline" @click="scrollToSection('features')">
                了解更多
              </Button>
            </div>
          </div>
        </Container>
      </section>

      <!-- 平台特色功能 -->
      <section id="features" class="features-section">
        <Container size="4">
          <div class="section-header">
            <Heading size="7" align="center">平台核心能力</Heading>
            <Text size="4" align="center" class="section-subtitle">
              基于先进技术架构，提供全方位数据安全监管服务
            </Text>
          </div>
          <div class="features-grid">
            <Card v-for="feature in features" :key="feature.id" class="feature-card">
              <div class="feature-icon">{{ feature.icon }}</div>
              <Heading size="4">{{ feature.title }}</Heading>
              <Text size="3">{{ feature.description }}</Text>
            </Card>
          </div>
        </Container>
      </section>

      <!-- 运营数据展示 -->
      <section class="stats-section">
        <Container size="4">
          <div class="section-header">
            <Heading size="7" align="center">平台运营数据</Heading>
          </div>
          <div class="stats-grid">
            <Card v-for="stat in operationalStats" :key="stat.id" class="stat-card">
              <div class="stat-content">
                <Text size="8" weight="bold" class="stat-value">{{ stat.value }}</Text>
                <Text size="4" class="stat-title">{{ stat.title }}</Text>
                <Text size="2" class="stat-desc">{{ stat.description }}</Text>
              </div>
            </Card>
          </div>
        </Container>
      </section>

      <!-- 服务企业展示 -->
      <section class="enterprises-section">
        <Container size="4">
          <div class="section-header">
            <Heading size="7" align="center">服务企业</Heading>
            <Text size="4" align="center" class="section-subtitle">
              为行业领军企业提供专业的数据安全监测服务
            </Text>
          </div>
          <div class="enterprises-grid">
            <div v-for="enterprise in enterprises" :key="enterprise.id" class="enterprise-item">
              <div class="enterprise-logo">{{ enterprise.name.charAt(0) }}</div>
              <Text size="3" weight="medium">{{ enterprise.name }}</Text>
              <Badge size="1" :variant="getEnterpriseTypeVariant(enterprise.type)">
                {{ enterprise.type }}
              </Badge>
            </div>
          </div>
        </Container>
      </section>

      <!-- 政策动态 -->
      <section class="news-section">
        <Container size="4">
          <div class="section-header">
            <Heading size="7" align="center">政策动态</Heading>
          </div>
          <div class="news-grid">
            <Card v-for="news in newsItems" :key="news.id" class="news-card">
              <div class="news-meta">
                <Badge>{{ news.category }}</Badge>
                <Text size="2" class="news-date">{{ news.date }}</Text>
              </div>
              <Heading size="4" class="news-title">{{ news.title }}</Heading>
              <Text size="3" class="news-summary">{{ news.summary }}</Text>
              <Button variant="ghost" size="2" class="news-link">
                查看详情 →
              </Button>
            </Card>
          </div>
        </Container>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <Container size="4">
        <div class="footer-content">
          <div class="footer-section">
            <Heading size="4">地理信息安全监测平台</Heading>
            <Text size="2">构建智能网联汽车时空数据安全监管体系</Text>
          </div>
          <div class="footer-section">
            <Heading size="3">快速链接</Heading>
            <div class="footer-links">
              <Text size="2"><a href="#features">平台功能</a></Text>
              <Text size="2"><a href="#stats">运营数据</a></Text>
              <Text size="2"><a href="#news">政策动态</a></Text>
            </div>
          </div>
          <div class="footer-section">
            <Heading size="3">联系我们</Heading>
            <Text size="2">技术支持：<EMAIL></Text>
            <Text size="2">监管咨询：<EMAIL></Text>
          </div>
        </div>
        <div class="footer-bottom">
          <Text size="2">© 2025 地理信息安全监测平台. 保留所有权利.</Text>
        </div>
      </Container>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Button, Container, Heading, Text, Card, Badge } from '@radix-ui/themes'

const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const getEnterpriseTypeVariant = (type: string) => {
  switch (type) {
    case '汽车企业': return 'blue'
    case '地图服务商': return 'green'
    case '智驾方案提供商': return 'orange'
    case '平台运营商': return 'purple'
    default: return 'gray'
  }
}

const features = [
  {
    id: 1,
    icon: '🗺️',
    title: '地理围栏监控',
    description: '动态展示地理围栏范围，实时标记车端和云端分布节点，支持风险热力图展示'
  },
  {
    id: 2,
    icon: '📊',
    title: '分级风险预警',
    description: '车端和云端风险分级显示，高风险闪烁弹窗、中风险呼吸灯提醒、低风险汇总报告'
  },
  {
    id: 3,
    icon: '📋',
    title: '备案审核管理',
    description: '企业信息、测绘资质、车辆信息、数据处理活动全流程备案审核管理'
  },
  {
    id: 4,
    icon: '🔍',
    title: '应急溯源追踪',
    description: '车端和云端数据处理活动全链路溯源，支持责任认定和事件调查'
  },
  {
    id: 5,
    icon: '🔗',
    title: '区块链存证',
    description: '审批记录、风险处置、检查任务等关键信息上链存证，防止篡改'
  },
  {
    id: 6,
    icon: '👥',
    title: '多端协同管理',
    description: '政府端监管、企业端自律，支持多监管员协同和权限精细化管理'
  }
]

const operationalStats = [
  {
    id: 1,
    value: '50万+',
    title: '接入车辆总数',
    description: '覆盖M类、N类、O类及低速无人驾驶装备'
  },
  {
    id: 2,
    value: '30+',
    title: '服务企业数量',
    description: '汽车企业、地图服务商、智驾方案提供商、平台运营商'
  },
  {
    id: 3,
    value: '3分钟',
    title: '实时数据更新',
    description: '车端和云端处理活动信息实时上报'
  },
  {
    id: 4,
    value: '11个',
    title: '数据处理环节',
    description: '收集、存储、传输、加工、提供、公开、销毁等全覆盖'
  },
  {
    id: 5,
    value: '4级',
    title: '风险预警等级',
    description: '特别重大、重大、较大、一般风险分级管理'
  },
  {
    id: 6,
    value: 'PB级',
    title: '数据存储能力',
    description: '支持海量时空数据安全存储和快速检索'
  }
]

const enterprises = [
  { id: 1, name: '北京汽车集团', type: '汽车企业' },
  { id: 2, name: '吉利汽车研究院', type: '汽车企业' },
  { id: 3, name: '上海汽车集团', type: '汽车企业' },
  { id: 4, name: '长城汽车', type: '汽车企业' },
  { id: 5, name: '东风商用车', type: '汽车企业' },
  { id: 6, name: '郑州宇通客车', type: '汽车企业' },
  { id: 7, name: '蔚马汽车技术', type: '汽车企业' },
  { id: 8, name: '阿里巴巴网络技术', type: '地图服务商' },
  { id: 9, name: '华为技术', type: '智驾方案提供商' },
  { id: 10, name: '杭州飞步科技', type: '智驾方案提供商' },
  { id: 11, name: '百度地图', type: '地图服务商' },
  { id: 12, name: '高德地图', type: '地图服务商' }
]

const newsItems = [
  {
    id: 1,
    category: '政策法规',
    date: '2025-01-15',
    title: '智能网联汽车时空数据安全监测平台通信协议规范发布',
    summary: '规范了企业平台与政府监测平台之间的通信协议，包括TCP/IP网络控制协议、数据包结构、传输方式及安全机制等内容。'
  },
  {
    id: 2,
    category: '技术标准',
    date: '2025-01-10',
    title: '智能网联汽车时空数据安全风险项（类别）清单征求意见稿',
    summary: '涵盖智能网联汽车时空数据的定义、分类分级及风险识别等重要内容，为风险预警提供标准依据。'
  },
  {
    id: 3,
    category: '行业动态',
    date: '2025-01-08',
    title: '四级分布式监管架构正式启动运行',
    summary: '国家-属地-企业-终端四级监管体系全面投入使用，实现对智能网联汽车时空数据的全覆盖监管。'
  },
  {
    id: 4,
    category: '监管公告',
    date: '2025-01-05',
    title: '关于开展智能网联汽车数据处理活动备案工作的通知',
    summary: '要求相关企业按照规定进行数据处理活动备案，包括企业信息、车辆信息、测绘资质等内容。'
  },
  {
    id: 5,
    category: '技术指南',
    date: '2025-01-03',
    title: '地理信息安全处理技术应用指南发布',
    summary: '详细介绍脱敏技术、坐标偏转技术、VMS数据融合技术、地理信息围栏技术等安全处理技术的应用要求。'
  },
  {
    id: 6,
    category: '合规指导',
    date: '2025-01-01',
    title: '智能网联汽车企业数据安全合规自查指南',
    summary: '为企业提供数据安全合规自查工具和最佳实践案例，帮助企业建立健全数据安全管理体系。'
  }
]

onMounted(() => {
  // 初始化主题
  document.documentElement.setAttribute('data-theme', 'light')
})
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: var(--color-background);
}

/* 导航栏样式 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--color-background);
  border-bottom: 1px solid var(--gray-6);
  backdrop-filter: blur(8px);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  width: 40px;
  height: 40px;
}

.brand-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-11);
  margin: 0;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 英雄区域样式 */
.hero-section {
  padding: 6rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, var(--accent-2) 0%, var(--accent-3) 100%);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--accent-11) 0%, var(--accent-9) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  margin-bottom: 3rem;
  color: var(--gray-11);
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  color: var(--accent-11);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--gray-10);
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 功能特色区域 */
.features-section {
  padding: 6rem 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-subtitle {
  margin-top: 1rem;
  color: var(--gray-10);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  padding: 2rem;
  text-align: center;
  border: 1px solid var(--gray-6);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* 运营数据区域 */
.stats-section {
  padding: 6rem 2rem;
  background: var(--gray-2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  padding: 2rem;
  text-align: center;
  background: var(--color-background);
  border: 1px solid var(--gray-6);
  border-radius: 12px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-value {
  color: var(--accent-11);
}

.stat-title {
  font-weight: 600;
}

.stat-desc {
  color: var(--gray-10);
}

/* 服务企业区域 */
.enterprises-section {
  padding: 6rem 2rem;
}

.enterprises-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.enterprise-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--gray-6);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.enterprise-item:hover {
  border-color: var(--accent-7);
  background: var(--accent-2);
}

.enterprise-logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--accent-9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

/* 新闻动态区域 */
.news-section {
  padding: 6rem 2rem;
  background: var(--gray-2);
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.news-card {
  padding: 2rem;
  background: var(--color-background);
  border: 1px solid var(--gray-6);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.news-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.news-date {
  color: var(--gray-10);
}

.news-title {
  margin-bottom: 1rem;
  line-height: 1.4;
}

.news-summary {
  margin-bottom: 1.5rem;
  color: var(--gray-11);
  line-height: 1.6;
}

.news-link {
  align-self: flex-start;
}

/* 页脚样式 */
.footer {
  padding: 4rem 2rem 2rem;
  background: var(--gray-12);
  color: var(--gray-1);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  margin-bottom: 1rem;
  color: var(--gray-1);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  color: var(--gray-10);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent-9);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--gray-8);
  color: var(--gray-10);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .hero-section {
    padding: 4rem 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .features-grid,
  .stats-grid,
  .enterprises-grid,
  .news-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    margin-bottom: 2rem;
  }

  .features-section,
  .stats-section,
  .enterprises-section,
  .news-section {
    padding: 4rem 1rem;
  }
}
</style>
