import * as React from 'react';
import { dataListRootPropDefs, dataListItemPropDefs, dataListLabelPropDefs } from './data-list.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type DataListRootOwnProps = GetPropDefTypes<typeof dataListRootPropDefs>;
interface DataListRootProps extends ComponentPropsWithout<'dl', RemovedProps>, MarginProps, DataListRootOwnProps {
}
declare const DataListRoot: React.ForwardRefExoticComponent<DataListRootProps & React.RefAttributes<HTMLDListElement>>;
type DataListItemOwnProps = GetPropDefTypes<typeof dataListItemPropDefs>;
interface DataListItemProps extends ComponentPropsWithout<'div', RemovedProps>, DataListItemOwnProps {
}
declare const DataListItem: React.ForwardRefExoticComponent<DataListItemProps & React.RefAttributes<HTMLDivElement>>;
type DataListLabelOwnProps = GetPropDefTypes<typeof dataListLabelPropDefs>;
interface DataListLabelProps extends ComponentPropsWithout<'dt', RemovedProps>, DataListLabelOwnProps {
}
declare const DataListLabel: React.ForwardRefExoticComponent<DataListLabelProps & React.RefAttributes<HTMLElement>>;
interface DataListValueProps extends ComponentPropsWithout<'dd', RemovedProps> {
}
declare const DataListValue: React.ForwardRefExoticComponent<DataListValueProps & React.RefAttributes<HTMLElement>>;
export { DataListRoot as Root, DataListItem as Item, DataListLabel as Label, DataListValue as Value, };
export type { DataListRootProps as RootProps, DataListItemProps as ItemProps, DataListLabelProps as LabelProps, DataListValueProps as ValueProps, };
//# sourceMappingURL=data-list.d.ts.map