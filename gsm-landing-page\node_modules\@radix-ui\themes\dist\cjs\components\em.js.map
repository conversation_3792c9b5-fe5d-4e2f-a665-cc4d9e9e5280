{"version": 3, "sources": ["../../../src/components/em.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { emPropDefs } from './em.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype EmElement = React.ElementRef<'em'>;\ntype EmOwnProps = GetPropDefTypes<typeof emPropDefs>;\ninterface EmProps extends ComponentPropsWithout<'em', RemovedProps>, EmOwnProps {}\nconst Em = React.forwardRef<EmElement, EmProps>((props, forwardedRef) => {\n  const { asChild, className, ...emProps } = extractProps(props, emPropDefs);\n  const Comp = asChild ? Slot.Root : 'em';\n  return <Comp {...emProps} ref={forwardedRef} className={classNames('rt-Em', className)} />;\n});\nEm.displayName = 'Em';\n\nexport { Em };\nexport type { EmProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,QAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBACvBC,EAAqB,oBAErBC,EAA2B,yBAC3BC,EAA6B,uCAQ7B,MAAMN,EAAKE,EAAM,WAA+B,CAACK,EAAOC,IAAiB,CACvE,KAAM,CAAE,QAAAC,EAAS,UAAAC,EAAW,GAAGC,CAAQ,KAAI,gBAAaJ,EAAO,YAAU,EACnEK,EAAOH,EAAU,OAAK,KAAO,KACnC,OAAOP,EAAA,cAACU,EAAA,CAAM,GAAGD,EAAS,IAAKH,EAAc,aAAW,EAAAK,SAAW,QAASH,CAAS,EAAG,CAC1F,CAAC,EACDV,EAAG,YAAc", "names": ["em_exports", "__export", "Em", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_em_props", "import_extract_props", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "className", "emProps", "Comp", "classNames"]}