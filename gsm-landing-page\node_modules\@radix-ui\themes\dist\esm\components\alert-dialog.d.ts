import * as React from 'react';
import { AlertDialog as AlertDialogPrimitive } from 'radix-ui';
import { Heading } from './heading.js';
import { Text } from './text.js';
import type { AlertDialogContentOwnProps } from './alert-dialog.props.js';
import type { ComponentPropsWithout, RemovedProps, ComponentPropsAs } from '../helpers/component-props.js';
interface AlertDialogRootProps extends React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Root> {
}
declare const AlertDialogRoot: React.FC<AlertDialogRootProps>;
interface AlertDialogTriggerProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Trigger, RemovedProps> {
}
declare const AlertDialogTrigger: React.ForwardRefExoticComponent<AlertDialogTriggerProps & React.RefAttributes<HTMLButtonElement>>;
interface AlertDialogContentProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Content, RemovedProps>, AlertDialogContentOwnProps {
    container?: React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Portal>['container'];
}
declare const AlertDialogContent: React.ForwardRefExoticComponent<Omit<AlertDialogContentProps, "ref"> & React.RefAttributes<HTMLDivElement>>;
type AlertDialogTitleProps = ComponentPropsWithout<typeof Heading, 'asChild'>;
declare const AlertDialogTitle: React.ForwardRefExoticComponent<AlertDialogTitleProps & React.RefAttributes<HTMLHeadingElement>>;
type AlertDialogDescriptionProps = ComponentPropsAs<typeof Text, 'p'>;
declare const AlertDialogDescription: React.ForwardRefExoticComponent<AlertDialogDescriptionProps & React.RefAttributes<HTMLParagraphElement>>;
interface AlertDialogActionProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Action, RemovedProps> {
}
declare const AlertDialogAction: React.ForwardRefExoticComponent<AlertDialogActionProps & React.RefAttributes<HTMLButtonElement>>;
interface AlertDialogCancelProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Cancel, RemovedProps> {
}
declare const AlertDialogCancel: React.ForwardRefExoticComponent<AlertDialogCancelProps & React.RefAttributes<HTMLButtonElement>>;
export { AlertDialogRoot as Root, AlertDialogTrigger as Trigger, AlertDialogContent as Content, AlertDialogTitle as Title, AlertDialogDescription as Description, AlertDialogAction as Action, AlertDialogCancel as Cancel, };
export type { AlertDialogRootProps as RootProps, AlertDialogTriggerProps as TriggerProps, AlertDialogContentProps as ContentProps, AlertDialogTitleProps as TitleProps, AlertDialogDescriptionProps as DescriptionProps, AlertDialogActionProps as ActionProps, AlertDialogCancelProps as CancelProps, };
//# sourceMappingURL=alert-dialog.d.ts.map