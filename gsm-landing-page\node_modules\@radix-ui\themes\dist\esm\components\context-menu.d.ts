import * as React from 'react';
import { ContextMenu as ContextMenuPrimitive } from 'radix-ui';
import { contextMenuContentPropDefs, contextMenuItemPropDefs, contextMenuCheckboxItemPropDefs, contextMenuRadioItemPropDefs } from './context-menu.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
interface ContextMenuRootProps extends React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Root> {
}
declare const ContextMenuRoot: React.FC<ContextMenuRootProps>;
interface ContextMenuTriggerProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Trigger, RemovedProps> {
}
declare const ContextMenuTrigger: React.ForwardRefExoticComponent<ContextMenuTriggerProps & React.RefAttributes<HTMLSpanElement>>;
type ContextMenuContentOwnProps = GetPropDefTypes<typeof contextMenuContentPropDefs>;
type ContextMenuContentContextValue = ContextMenuContentOwnProps;
interface ContextMenuContentProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Content, RemovedProps>, ContextMenuContentContextValue {
    container?: React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Portal>['container'];
}
declare const ContextMenuContent: React.ForwardRefExoticComponent<ContextMenuContentProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuLabelProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Label, RemovedProps> {
}
declare const ContextMenuLabel: React.ForwardRefExoticComponent<ContextMenuLabelProps & React.RefAttributes<HTMLDivElement>>;
type ContextMenuItemOwnProps = GetPropDefTypes<typeof contextMenuItemPropDefs>;
interface ContextMenuItemProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Item, RemovedProps>, ContextMenuItemOwnProps {
}
declare const ContextMenuItem: React.ForwardRefExoticComponent<ContextMenuItemProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuGroupProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Group, RemovedProps> {
}
declare const ContextMenuGroup: React.ForwardRefExoticComponent<ContextMenuGroupProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuRadioGroupProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.RadioGroup, RemovedProps> {
}
declare const ContextMenuRadioGroup: React.ForwardRefExoticComponent<ContextMenuRadioGroupProps & React.RefAttributes<HTMLDivElement>>;
type ContextMenuRadioItemOwnProps = GetPropDefTypes<typeof contextMenuRadioItemPropDefs>;
interface ContextMenuRadioItemProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.RadioItem, RemovedProps>, ContextMenuRadioItemOwnProps {
}
declare const ContextMenuRadioItem: React.ForwardRefExoticComponent<ContextMenuRadioItemProps & React.RefAttributes<HTMLDivElement>>;
type ContextMenuCheckboxItemOwnProps = GetPropDefTypes<typeof contextMenuCheckboxItemPropDefs>;
interface ContextMenuCheckboxItemProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.CheckboxItem, RemovedProps>, ContextMenuCheckboxItemOwnProps {
}
declare const ContextMenuCheckboxItem: React.ForwardRefExoticComponent<ContextMenuCheckboxItemProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuSubProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Sub, RemovedProps> {
}
declare const ContextMenuSub: React.FC<ContextMenuSubProps>;
interface ContextMenuSubTriggerProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.SubTrigger, RemovedProps> {
}
declare const ContextMenuSubTrigger: React.ForwardRefExoticComponent<ContextMenuSubTriggerProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuSubContentProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.SubContent, RemovedProps> {
    container?: React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Portal>['container'];
}
declare const ContextMenuSubContent: React.ForwardRefExoticComponent<ContextMenuSubContentProps & React.RefAttributes<HTMLDivElement>>;
interface ContextMenuSeparatorProps extends ComponentPropsWithout<typeof ContextMenuPrimitive.Separator, RemovedProps> {
}
declare const ContextMenuSeparator: React.ForwardRefExoticComponent<ContextMenuSeparatorProps & React.RefAttributes<HTMLDivElement>>;
export { ContextMenuRoot as Root, ContextMenuTrigger as Trigger, ContextMenuContent as Content, ContextMenuLabel as Label, ContextMenuItem as Item, ContextMenuGroup as Group, ContextMenuRadioGroup as RadioGroup, ContextMenuRadioItem as RadioItem, ContextMenuCheckboxItem as CheckboxItem, ContextMenuSub as Sub, ContextMenuSubTrigger as SubTrigger, ContextMenuSubContent as SubContent, ContextMenuSeparator as Separator, };
export type { ContextMenuRootProps as RootProps, ContextMenuTriggerProps as TriggerProps, ContextMenuContentProps as ContentProps, ContextMenuLabelProps as LabelProps, ContextMenuItemProps as ItemProps, ContextMenuGroupProps as GroupProps, ContextMenuRadioGroupProps as RadioGroupProps, ContextMenuRadioItemProps as RadioItemProps, ContextMenuCheckboxItemProps as CheckboxItemProps, ContextMenuSubProps as SubProps, ContextMenuSubTriggerProps as SubTriggerProps, ContextMenuSubContentProps as SubContentProps, ContextMenuSeparatorProps as SeparatorProps, };
//# sourceMappingURL=context-menu.d.ts.map