{"version": 3, "sources": ["../../../src/components/em.props.tsx"], "sourcesContent": ["import { asChildPropDef } from '../props/as-child.prop.js';\nimport { textWrapPropDef } from '../props/text-wrap.prop.js';\nimport { truncatePropDef } from '../props/truncate.prop.js';\n\nconst emPropDefs = {\n  ...asChildPropDef,\n  ...truncatePropDef,\n  ...textWrapPropDef,\n};\n\nexport { emPropDefs };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,gBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA+B,qCAC/BC,EAAgC,sCAChCC,EAAgC,qCAEhC,MAAMJ,EAAa,CACjB,GAAG,iBACH,GAAG,kBACH,GAAG,iBACL", "names": ["em_props_exports", "__export", "emPropDefs", "__toCommonJS", "import_as_child_prop", "import_text_wrap_prop", "import_truncate_prop"]}