地理信息安全监测平台通信协议 - 技术评

审版

版本 : V4.1 (20250717- 合并修订版)

1. 范围

本⽂档规定了企业平台（客⼾端）与政府监测平台（服务端）之间的通信协议，包括通信连

接、数据包结构、数据单元格式等。本⽂档适⽤于所有接⼊中国汽⻋时空数据安全监测平台的

企业平台。

2. 术语和定义

• 客⼾端平台 : 指代进⾏数据处理活动的企业平台、地图服务商平台等。

• 服务端平台 : 指代政府侧的地理信息安全监测平台。

• 定点数 : ⼀种使⽤整型数据表⽰浮点数的⽅法，通过乘以⼀个固定的缩放因⼦来实现。

3. 数据交换流程

客⼾端平台与服务端平台的整体通信流程如下图所⽰。

4. 通信连接

4.1 通信协议

a) 通信协议结构以 TCP/IP ⽹络控制协议作为底层通信承载协议。如图所⽰。

图 2 ⾃动驾驶地理信息数据安全监测系统通信协议栈

b) 平台间的通信连接与数据传输应满⾜本⽂档的相关要求。

c) 终端到企业平台的间的通信连接与数据传输宜参照本⽂要求执⾏。

4.2 连接建⽴

a) 客⼾端平台向服务端平台发起通信连接请求，当通信链路连接建⽴后，客⼾端平台应⾃动向

服务端平台发送登⼊信息进⾏⾝份识别，服务端平台应对接收到的数据进⾏校验：校验正确

时，服务端平台应返回成功应答；校验错误时，服务端平台应存储错误数据记录并通知客⼾端

平台。

b) 客⼾端平台应在接收到服务端平台的应答指令后完成本次登⼊传输；客⼾端平台在规定时间

内未收到应答指令，应每间隔 1min 重新进⾏登⼊；若连续重复 3 次登⼊⽆应答，应间隔

30min 后，继续重新链接。链接成功并登⼊后，应将链接中断期间本地存储的未成功发送的数

据，通过补发机制进⾏上报。重复登⼊间隔时间可以设置。

4.3 连接维持

连接建⽴和校验成功后，在没有正常数据包传输的情况下，客⼾端平台应周期性向服务端平台

发送⼼跳消息，服务端平台收到后向客⼾端平台发送平台通⽤应答消息，发送周期默认为 60

秒。

4.4 数据传输

客⼾端平台登⼊成功后，应向服务端平台周期性上报与地理信息数据处理活动相关的信息数

据。当客⼾端平台发⽣预定义的客观系统状态事件时，应⽴即上报事件数据。

表 1 事件类型标志定义

服务端平台根据“数据处理活动信息”和“事件数据”，通过其内置的规则引擎、统计算法及机

器学习模型，对潜在的数据安全⻛险进⾏识别、研判和告警。⻛险的识别能⼒与责任主体为服

务端平台，客⼾端平台仅负责忠实、完整地按本协议上报其活动与状态。当客⼾端平台向服务

端平台上报信息时，服务端平台应对接收到的数据进⾏校验。当校验正确时，服务端平台做正

确应答；当校验错误时，服务端平台做错误应答并丢弃该数据。

4.5 控制与查询

事件分类标识 事件类型 记录时间 记录数据

0x01 ⻋端数据泄露 / 丢失 检测到事件的时刻 *** 章节

0x02 云端数据泄露 / 丢失 *** 章节

服务端平台根据需要向客⼾端平台发送控制指令（命令标识 0x83），如数据查询、参数配置

等。客⼾端平台应在规定时间内进⾏响应。

4.5.1 数据查询的 HTTPS 上报实现

对于数据查询类指令（控制类型为 0x01: 数据查询请求），客⼾端平台应通过独⽴的 HTTPS 通

道向服务端平台上报所查询的历史数据，具体流程如下：

1. 指令下发 : 服务端平台通过本协议定义的 TCP ⻓连接，向客⼾端平台下发控制指令（命令标

识 0x83），其中“控制类型”为 0x01: 数据查询请求。指令中应包含查询任务的唯⼀ ID、时

间范围、数据类型等参数。

2. 指令确认 : 客⼾端平台收到查询指令后，应⽴即通过 TCP 连接返回“平台通⽤应答”（命令

标识 0x81），确认已接收该指令。

3. 数据准备与上报 : 客⼾端平台根据指令要求，在本地数据库中异步查询并打包历史数据。完

成后，通过 HTTP/S POST 请求，将数据提交⾄服务端平台指定的 HTTPS 数据接收地址。

该地址由平台在备案阶段提供。

4. 接⼝规范 :

◦ 认证机制 : HTTPS 请求的 Header 中必须包含有效的认证令牌（如 API Key/Secret 或动

态⽣成的 JWT），⽤于服务端进⾏⾝份鉴权。

◦ 数据格式 : POST 请求的 Body 应为 application/json 格式，数据内容遵循本协议第 6.4

节定义的实时信息上报格式，可包含⼀个或多个信息体。

◦ 上报回执 : 服务端 HTTPS 接⼝在成功接收并校验数据后，应返回 HTTP 200 OK 状态

码。若数据或请求格式错误，则返回 4xx 系列状态码及错误说明。客⼾端平台需记录上

报结果。

4.6 平台通⽤应答

平台通⽤应答（命令标识 0x81）是服务端对客⼾端⼤部分上⾏消息的统⼀应答机制。除平台登

⼊、链路检测等有专⻔应答消息的指令外，服务端在接收到客⼾端的实时信息上报、事件数据

上报、平台登出、补发数据消息后，均应返回通⽤应答。

通⽤应答消息体中需包含应答流⽔号（对应上⾏消息的流⽔号）、应答 ID（对应上⾏消息的命

令标识）和应答结果（成功 / 失败 / 消息有误），以便客⼾端对消息发送状态进⾏确认。

4.7 补发机制

为确保通信中断期间的数据完整性，客⼾端平台必须实现可靠的数据补发机制。

4.7.1 补发触发条件

当客⼾端平台⽆法将数据成功发送⾄服务端平台时（包括但不限于 TCP 连接中断、发送后未在

规定时间内收到通⽤应答），应⽴即启动补发机制，将该数据转⼊本地补发队列。

4.7.2 本地缓存要求

a) 缓存队列 : 客⼾端平台应建⽴⼀个或多个先进先出（FIFO）的本地缓存队列，⽤于存储待补

发的数据。关键事件数据和实时业务数据可分队列存储，⾼优先级数据优先补发。

b) 缓存能⼒ : 本地缓存⾄少能容纳连续 72 ⼩时的平均数据量。当缓存空间使⽤率超过 90%

时，应记录⼀条“系统异常”事件。

4.7.3 补发流程与策略

a) 连接恢复 : 与服务端平台重连并成功登⼊后，客⼾端平台应⽴即检查本地补发队列。

b) 补发操作 : 若队列⾮空，则开始数据补发流程。补发数据的数据单元格式与实时上报（6.4

节）完全相同，但必须使⽤命令标识 0x04 进⾏上报。

c) 流量控制 : 补发数据时，应采⽤平滑的发送策略 , 避免瞬间流量冲击。建议补发速率不超过正

常实时上报速率的 2 倍，且可由服务端通过参数配置指令进⾏调整。

d) 补发超时与重试 : 单条补发数据的发送同样需要等待服务端通⽤应答。若超时未收到应答，

应按 1, 2, 5, 10... 分钟的退避策略进⾏重试，最多重试 5 次。5 次失败后，记录本地⽇志，不再

对该单条数据进⾏⾃动补发，等待⼿动⼲预或控制指令。

4.7.4 数据⼀致性

为避免数据重复，实时上报和补发上报的所有信息体均需包含全局唯⼀的数据包 ID。服务端平

台负责根据此 ID 进⾏数据的去重处理。

4.7.5 补发数据优先级

为确保⾼⻛险数据在通信恢复后得到优先处理，客⼾端在执⾏补发操作时，应遵循以下优先级

策略。

4.8 应答机制

本协议采⽤“请求 - 应答”的通信模型，以确保数据交互的可靠性。

客⼾端请求 : 客⼾端主动向服务端发送的请求类或数据上报类消息（如平台登⼊、实时信息

上报、事件信息上报、连接⼼跳），都应被视为⼀次“请求”。

服务端应答 : 服务端在接收到客⼾端的请求后，必须在规定的超时时间内（建议为 5 秒）返

回⼀个对应的“应答”消息（如登⼊应答、通⽤应答、⼼跳应答）。

超时与重试 : 若客⼾端在发送请求后，在超时时间内未收到服务端的应答，应认为本次通信

失败。对于需要可靠传输的数据上报类消息，应⽴即启动重试或补发机制（参⻅ 4.7 节）。

此应答机制构成了平台间通信的基础，保证了指令和数据的可达性与⼀致性。

优先级 数据类型 说明

⾼ 事件数据上报 (0x06) 所有事件数据，特别是涉及数据泄露(0x10)、

违规出境(0x11)等⾼⻛险事件，必须最优先补

发。

涉及核⼼ / 重要数据的实

时信息 (0x20)

根据《时空数据安全⻛险项（类别）清单》判

定为“核⼼”或“重要”的数据处理活动信

息。

中 涉及⼀般数据的实时信

息 (0x20)

除⾼优先级外的⼀般性数据处理活动信息。

低 平台⼼跳及其他 链路维持类消息，通常在实时发送失败后⽆需

补发，但在特定诊断场景下可能需要。

1.

2.

3.

图 3 客⼾端平台与服务端平台整体通信流程图

4.9 连接断开

服务端平台应根据以下情况断开与客⼾端平台的会话连接：

a) 客⼾端平台主动断开 TCP 连接

b) 相同⾝份的企业数据中⼼建⽴新连接

c) 在⼀定的时间内未收到客⼾端平台⼼跳信息（默认 300 秒）

客⼾端平台应根据以下情况断开与服务端平台的会话连接：

a) 服务端平台主动断开 TCP 连接

b) 相同⾝份的企业数据中⼼建⽴新连接

c) 数据通信链路正常，达到重传次数后仍未收到应答

5. 数据包结构和定义

5.1 数据类型

协议中传输的数据类型⻅表 7-1。

表 5-1 数据类型

注：协议中的地理位置、⾥程等计量数据，采⽤定点数⽅式进⾏编码。即，使⽤⼀个⻓整型

（如 DWORD）来存储原始浮点数值乘以⼀个固定倍数（如 10^6）后的结果，接收⽅在解析时

再除以该倍数还原。此⽅法可保证跨平台的字节序和精度⼀致性。

5.2 传输规则

协议采⽤⼤端模式（big-endian）的⽹络字节序来传递字和双字。

• 字节（BYTE） 的传输约定：按照字节流的⽅式传输；

数据类型 描述及要求

BYTE ⽆符号单字节整型（字节，8 位）

WORD ⽆符号双字节整型（字，16 位）

DWORD ⽆符号四字节整型（双字，32 位）

BYTE[n] n 字节

STRING ASCII 字符码，若⽆数据则以 0x00 终结

• 字（WORD） 的传输约定：先传递⾼⼋位，再传递低⼋位；

• 双字（DWORD） 的传输约定：先传递⾼ 24 位，然后传递⾼ 16 位，再传递⾼⼋位，最后传

递低⼋位。

5.3 消息结构

每条消息由起始符、消息头、消息体和校验码组成。

5.3.1 起始符

起始符固定为 ## (ASCII 码 : 0x230x23)。

5.3.2 转义规则

为避免消息体中出现与起始符 ## 或转义符 * (0x2A) 冲突，定义转义规则如下：

发送时，在计算完 CRC 校验码之后进⾏转义：

0x23 -> 0x2A, 0x01

0x2A -> 0x2A, 0x02

接收时，在校验 CRC 之前进⾏反转义：

0x2A, 0x01 -> 0x23

0x2A, 0x02 -> 0x2A

5.3.3 数据包结构

⼀个完整的数据包在转义前的结构定义⻅下表。

5.3.4 消息体属性

消息体属性字段为 WORD 类型，结构如下：

• 消息体⻓度 (bit0-9): 消息体的总⻓度，最⼤为 1023 字节。

• 加密⽅式 (bit10-12): 000 表⽰不加密；001 表⽰ SM4 加密；其他保留。

• 分包 (bit13): 1 表⽰消息体为⻓消息，进⾏了分包。0 表⽰不分包。

5.3.5 消息包封装项

当消息体属性中分包标识位为 1 时，消息头中应增加消息包封装项。

组成部分 定义 ⻓度 数据类型 描述及要求

消息头 消息 ID 1 BYTE 消息的唯⼀标识

消息体属性 2 WORD 定义消息体分包、加密及⻓

度

企业统⼀社会

信⽤代码

18 STRING[18] 企业的 18 位统⼀社会信⽤

代码

消息流⽔号 2 WORD 由发送⽅维护，从 0 开始循

环累加

消息包封装项 (可选) 当消息体属性中分包标识为

1 时存在

消息体 数据单元 N 数据单元格式和定义应符合

第 6 章要求

校验码 CRC-16 校验

码

2 WORD 从消息头到消息体尾部的

CRC-16(CCITT)校验值

位(bit) 15-14 13 12-10 9-0

定义 保留 分包 加密⽅式 消息体⻓度

5.3.6 校验码

校验码采⽤ CRC-16/CCITT-FALSE 算法，多项式为 X^16 + X^12 + X^5 + 1 (0x1021)。校验范围

为从消息头的第⼀个字节到消息体的最后⼀个字节。

5.4 命令单元

字段 ⻓度 数据类型 描述

消息总包数 2 WORD 该消息被分包后的总

包数

包序号 2 WORD 当前包的序号，从 1

开始

5.4.2 应答标志

消息 ID 定义 数据流向 备注

客⼾端消息 (上⾏)

0x01 客⼾端通⽤应答 C -> S 对服务端下⾏消息的通⽤应答

0x02 客⼾端⼼跳 C -> S ⽤于维持连接

0x03 平台登出 C -> S 客⼾端主动断开连接

0x04 补发信息上报 C -> S 上报历史数据处理活动信息

0x06 事件数据上报 C -> S 上报客⼾端数据安全相关事件

0x10 平台登⼊ C -> S 客⼾端鉴权请求

0x20 实时信息上报 C -> S 上报各数据处理活动信息

0x70 客⼾端密钥交换 C -> S 客⼾端上报公钥或对服务端密钥请求的响应

服务端消息 (下⾏)

0x81 服务端通⽤应答 S -> C 对客⼾端上⾏消息的通⽤应答

0x83 控制指令 S -> C 服务端向客⼾端下发控制类指令

0x90 平台登⼊应答 S -> C 对平台登⼊请求的专⻔应答

0xF0 平台密钥交换 S -> C 服务端下发公钥或请求客⼾端公钥

6. 数据单元格式和定义

6.1 通⽤应答

客⼾端通⽤应答 (0x01) 和服务端通⽤应答(0x81) 使⽤相同的消息

6.2 平台登⼊

6.3 平台登出

编码 定义 说明

0x01 成功 接收到的信息正确

0x02 错误 : 企业 ID 重复 企业 ID 重复错误

0x03 错误 : 企业 ID 不存在 企业 ID 未在系统中备案

0x04 错误 : 鉴权码错误 鉴权码验证失败

0xFE 命令 表⽰数据包为命令包 , ⽽⾮应答包

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

应答流⽔号 2 WORD 对应请求消息的流⽔号

应答 ID 1 BYTE 对应请求消息的 ID

结果 1 BYTE 0: 成功 / 确认 ; 1: 失败 ; 2: 消息有

误 ; 3: 不⽀持

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

登⼊流⽔号 2 WORD 从 1 开始 , 每⽇循环累加

平台统⼀社会信⽤代码 18 STRING[18] 企业的 18 位统⼀社会信⽤代码

平台密码 20 STRING 平台登⼊密码

平台类型 1 BYTE 0x01: 汽⻋企业 ; 0x02: 地图服务商 ;

0x03: 智驾⽅案提供商 ; 0x04: 平台运营

商 ; 0x05: 其他

客⼾端平台下线前，应向服务端平台发送平台登出消息（命令标识 0x03）。服务端应监控与客

⼾端平台的 TCP 连接状态。若连接在未收到登出消息的情况下异常中断，服务端应判定为⼀次

“平台异常登出”事件，并记录相关信息，作为⻛险评估的输⼊之⼀。

6.4 实时信息上报

本命令（命令标识 ）⽤于客⼾端平台向服务端平台实时上报数据处理活动信息。数据包

的数据单元中可封装⼀个或多个信息体，每个信息体由“信息类型标志”和“信息体”构成，

具体格式定义如下。

实时信息上报格式

表 * 实时信息上报总体格式

信息类型标志

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

登出流⽔号 2 WORD 与当次登⼊流⽔号⼀

致

登出原因 1 BYTE 0x01: 正常登出 ;

0x02: 通信异常 ;

0x03: 平台故障 ;

0x04: 超时登出

0x20

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

信息类型标志 (1) 1 BYTE 标识该信息体对应的

数据处理环节，定义

⻅ 表 8-3。

信息体 (1) — — 对应信息类型的数据

详情，结构根据信息

类型标志变化。

... ... ... ...

信息类型标志 (n) 1 BYTE ...

信息体 (n) — — ...

表 * 信息类型标志定义

6.4.1 ⻋端数据处理活动

收集阶段上报数据格式和定义 (⻋端)

类型编码 说明

0x01 ⻋端收集阶段数据处理活动信息

0x02 ⻋端存储阶段数据处理活动信息

0x03 ⻋端传输阶段数据处理活动信息

0x04 云端收集阶段数据处理活动信息

0x05 云端存储阶段数据处理活动信息

0x06 云端传输阶段数据处理活动信息

0x07 云端加⼯阶段数据处理活动信息

0x08 云端提供阶段数据处理活动信息

0x09 云端公开阶段数据处理活动信息

0x0a 云端销毁阶段数据处理活动信息

存储阶段上报数据格式和定义 (⻋端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

⻋辆 VIN 码 17 STRING[17] 17 位⻋辆唯⼀ VIN 码

定位状态 1 BYTE 状态位定义

经度 4 DWORD 以度为单位的值乘以 10^6

纬度 4 DWORD 以度为单位的值乘以 10^6

⾼度 4 DWORD 以⽶为单位的值乘以 100

数据类型 4 DWORD BitMap 格式。服务端可根据此字段判断后续

“采⽤的安全处理技术”是否完备。

业务形式 1 BYTE 0x01: 导航电⼦地图更新服务 ; 0x02: 研发测

试 ; 0x03: 众源更新 ; 0x04: 其他

安全处理技术 2 WORD BitMap 格式 , 按位表⽰。bit0: 地理围栏技

术 ; bit1: 位置数据保密处理技术 ; bit2: 属性脱

敏技术 ; bit3: ⾥程限制技术。

安全处理技术类型 1 BYTE bit0: ⾃研 ...(预留）

地图审图号 32 STRING 如果业务活动涉及⻋载地图的更新或使⽤，则

应填写有效审图号。若不涉及，则以 0x00 填

充。

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

⻋辆 VIN 码 17 STRING[17] 17 位⻋辆唯⼀ VIN 码

本次存储数据关联

⾥程

4 DWORD 本次上报的数据所对应的道路⾥程 , 精度

0.1km

坐标处理⼿段 1 BYTE 0x00: 未处理真实坐标 ; 0x01: 坐标偏转插件 ;

0x02: 坐标偏转算法 ; 0x03: 其他

访问控制状态 1 BYTE 0x01: 关闭访问控制 ; 0x02: 启⽤访问控制

加密存储状态 1 BYTE 是否采⽤加密存储 , 0x00: 未加密 ; 0x01:SM2;

0x02:SM4; 0x03:AES; 0x04:RSA; 0x05: 其他

传输阶段上报数据格式和定义 (⻋端)

6.4.2 云端数据处理活动

为实现对云端数据的全⽣命周期穿透式监管，所有云端数据处理活动的上报信息中，必须包含

“数据包唯⼀标识”。该标识在数据包⾸次于云端⽣成或⼊库时被创建，并在后续的存储、加

⼯、提供、公开、销毁等所有环节中保持不变且随之上报。

收集阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

⻋辆 VIN 码 17 STRING[17] 17 位⻋辆唯⼀ VIN 码

传输⽬的地 18 STRING[18] 接收⽅企业的 18 位统⼀社会信⽤代码

传输⽬的地 IP 16 BYTE[16] IPv4 地址使⽤ IPv4 映射的 IPv6 地址格式表

⽰

传输覆盖⾥程 4 DWORD 传输数据覆盖⾥程 , 0.1km 精度

坐标处理标志 1 BYTE 0x01: 未传输真实坐标 ; 0x02: 传输真实坐标

坐标处理⼿段 1 BYTE 0x01: 坐标偏转插件 ; 0x02: 坐标偏转算法 ;

0x03: 其他

传输区域类型 1 BYTE 0x01: 境内 ; 0x02: 境外

通信⽹络类型 1 BYTE 0x01: 公共⽹络 ; 0x02: 专⽤⽹络 ; 0x03: 国家

认定⽹络 ; 0x04: 其他

安全传输协议 1 BYTE 0x01:HTTP; 0x02:HTTPS; 0x03: 国密通道 ;

0x04: 其他

⻋外传输功能 1 BYTE 0x01: 具备 ; 0x02: 不具备

存储阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据包唯⼀标识 36 STRING 数据的唯⼀标识(推荐采⽤ UUID 格式)。

数据重要程度 1 BYTE 0x01: ⼀般数据 ; 0x02: 重要数据

数据来源类型 1 BYTE 0x01: 本企业研采⻋ ; 0x02: 本企业量产⻋ ;

0x03: 第三⽅企业提供 ; 0x04: 其他

数据来源⽅标识 32 STRING 根据“数据来源类型”填写⻋端 VIN 或第三

⽅企业统⼀社会信⽤代码。

数据⽤途 1 BYTE 0x01: 数据汇聚及脱敏处理 ; 0x02: 导航电⼦

地图制作 ; 0x03: 场景库制作及服务

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀般数据操

作⼈员

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据重要程度 1 BYTE 0x01: ⼀般数据 0x02: 重要数据

存储设备类型 1 BYTE 0x01: 服务器 ; 0x02: 存储阵列 ; 0x03: ⽹络存

储 ; 0x04: 硬盘 ; 0x05: 其他

存储区域标识 1 BYTE 0x01: 境内存储 ; 0x02: 境外存储

存储设备 IP ⽤于识别是在境内还是境外

存储区类型 1 BYTE 0x01: 原始数据处理区 ; 0x02: 数据共享区 ;

0x03: 数据中转区 ; 0x04: 其他区域

数据分区标识 1 BYTE 0x01: 已分区存储 ; 0x02: 未分区存储

存储保障标识 1 BYTE Bit0: 完整性 , Bit1: 真实性 , Bit2: 可⽤性 (1=

保障 , 0= 未保障)

脱敏处理标识 1 BYTE 0x01: 已脱敏 ; 0x02: 未脱敏

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀般数据操

作⼈员

传输阶段上报数据格式和定义 (云端)

加⼯阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据重要程度 1 BYTE 0x01: ⼀般 0x02: 重要

传输⽬的地 18 STRING[18] 接收⽅企业的 18 位统⼀社会信⽤

代码

通信通道类型 1 BYTE 0x01: 国密通道 ; 0x02: ⾮国密通道

公共⽹络使⽤标识 1 BYTE 0x01: 使⽤公共信息⽹络 ; 0x02: 不

使⽤

传输⽅式 1 BYTE 0x01: ⽹络传输 ; 0x02: 硬盘拷⻉ ;

0x03: 其他

通信⽹络类型 1 BYTE 0x01: 公共⽹络 ; 0x02: 专⽤⽹络 ;

0x03: 国家认定⽹络 ; 0x04: 其他

安全传输协议 1 BYTE 0x01:HTTP; 0x02:HTTPS; 0x03: 国

密通道 ; 0x04: 其他

硬盘传输记录 1 BYTE 0x01: 记录完整 ; 0x02: 记录缺失

硬盘安全措施 1 BYTE 0x01: 安全措施合规 ; 0x02: 不合规

传输保障标识 1 BYTE Bit0: 完整性 , Bit1: 真实性 , Bit2: 可

⽤性 (1= 保障 , 0= 未保障)

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀

般数据操作⼈员

提供阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据包唯⼀标识 36 STRING 被加⼯的原始数据包 ID

加⼯类型 1 BYTE 0x01: 数据汇聚及脱敏处理 ; 0x02:

导航电⼦地图制作 ; 0x03: 场景库制

作及服务 ; 0x04: 互联⽹地图服务 ;

0x05: 其他

平⾯位置精度合规

性

1 BYTE 0x01: 符合要求 ; 0x02: 不符合要求

脱敏状态 1 BYTE 0x01: 已脱敏 ; 0x02: 未脱敏

是否⽣成新数据包 1 BYTE 0x01: 是 ; 0x02: 否

新数据包数量 2 WORD 若⽣成新数据包，此处填写⽣成的

新数据包个数。否则填 0。

新数据包 ID 列表 N STRING 若⽣成了新数据包，此处填写所有

新包的唯⼀标识 ID，以逗号分隔。

新数据包数据类型 4 DWORD BitMap 格式，标识新数据包中包

含的数据类型。

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀

般数据操作⼈员

公开阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据包 ID 列表 N STRING 本次提供操作所涉及的所有数据包

的唯⼀标识 ID，以逗号分隔。

提供的数据包数量 2 WORD 本次提供的数据包总数

接收⽅类型 1 BYTE 0x01: 境内⾮外商投资企业 ; 0x02:

境内外商投资企业 ; 0x03: 境外接收

⽅ ; 0x04: 个⼈

接收⽅安全能⼒ 1 BYTE 0x01: 安全能⼒满⾜ ; 0x02: 不⾜

是否满⾜豁免条件 1 BYTE 0x01: 是 ; 0x02: 否

采⽤的安全处理技

术

2 WORD BitMap 格式

合同协议签署状态 1 BYTE 0x01: ⽆合同协议 ; 0x02: 有合同协

议

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀

般数据操作⼈员

销毁阶段上报数据格式和定义 (云端)

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据包 ID 列表 N STRING 公开的数据包唯⼀标识 ID, 以逗号

分隔

公开渠道与范围 256 STRING 填写公开渠道的 URL 或平台 / 媒介

名称 , 并明确可访问范围。

地图审核程序执⾏

机构

1 BYTE

地图审核程序执⾏

时间

脱敏处理标志 1 BYTE 0x01: 未经脱敏处理 ; 0x02: 已经安

全处理

⻛险评估状态 1 BYTE 0x01: 未执⾏⻛险评估 ; 0x02: 已执

⾏

安全⻛险评估机构

代码

18 STRING[18] 评估机构的 18 位统⼀社会信⽤代

码

⻛险评估执⾏时间

数据审查状态 1 BYTE 0x01: 履⾏安全审查 ; 0x02: 未履⾏

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈员 ; 0x02: ⼀

般数据操作⼈员

6.5 事件数据上报

本命令（命令标识 0x06）⽤于客⼾端平台在监测到特定的数据安全相关事件时，实时向服务端

平台上报。

6.5.1 事件数据上报总体格式

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

数据包 ID 列表 N STRING 本次销毁的所有数据

包的唯⼀标识 ID，以

逗号分隔。

销毁的数据包数量 2 WORD 本次销毁的数据包总

数。

销毁⽅式 1 BYTE 0x01: 逻辑删除 ;

0x02: ⽂件覆盖 / 低级

格式化 ; 0x03: 物理销

毁

审批状态 1 BYTE 0x01: 未经审批 ;

0x02: 已经审批

销毁流程状态 1 BYTE 0x01: 未完成 ; 0x02:

已完成

操作员⾝份 1 BYTE 0x01: 重要数据操作⼈

员 ; 0x02: ⼀般数据操

作⼈员

6.5.2 事件类型定义

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件数量(N) 1 BYTE 本次上报的事件总

数。

事件类型(1) 1 BYTE 第 1 个事件的类型，

定义⻅下表。

事件信息体⻓度(1) 2 WORD 第 1 个事件信息体的

字节⻓度。

事件信息体(1) 第 1 个事件的详情，

结构随事件类型变

化。

...

6.5.3 事件信息体格式

0x01 通信链路中断事件数据格式

事件类型代码 事件名称 上报主体 触发场景 / 说明

⻋端事件

0x01 通信链路中断 ⻋端 客⼾端检测到与服务端⻓时间⽆法

连接。

0x02 数据缓存溢出 ⻋端 因链路中断导致本地缓存空间不

⾜，部分待上报数据丢失。

0x03 平台服务异常 ⻋端 客⼾端⾃⾝关键服务(如数据采

集、加密模块)发⽣故障。

0x04 核⼼安全策略异常 ⻋端 ⻋端核⼼安全处理技术模块发⽣故

障或被禁⽤。

0x05 - 0x0F (预留) ⻋端 -

云端事件

0x10 数据泄露 / 丢失 云端 敏感数据在存储、传输等环节发⽣

⾮授权访问或丢失。

0x11 核⼼安全策略异常 云端 如加密模块、脱敏⼯具等核⼼安全

组件发⽣故障或被禁⽤。

0x12 权限越权 云端 监测到账号（特别是⾼权限账号）

的异常数据访问⾏为。

0x13 数据补发缓存池超

限

云端 ⽤于数据补发的本地缓存空间使⽤

率超过阈值（如 90%）。

0x14 平台异常登出 云端(服务端) 由服务端⽣成，记录客⼾端的⾮正

常离线。

0x15 - 0xFF (预留) 云端 -

0x02 数据缓存溢出事件数据格式

0x03 平台服务异常事件数据格式

0x04 ⻋端核⼼安全策略异常事件数据格式

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD￾hh-mm-ss

中断持续时间 4 DWORD 单位：秒

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD￾hh-mm-ss

丢失数据条数 4 DWORD 溢出导致丢失的数据

记录条数

丢失数据起始消息 ID 36 STRING 丢失数据段的第⼀条

消息的唯⼀标识 ID

（若可追溯）

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD￾hh-mm-ss

异常模块名称 32 STRING 发⽣异常的内部服务

或模块名称

错误代码 4 DWORD 具体的错误码

0x10 数据泄露 / 丢失事件数据格式

0x11 云端核⼼安全策略异常事件数据格式

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD-hh-mm-ss

异常模块类型 1 BYTE 0x01: 保密处理 ; 0x02: 地理围栏 ;

0x03: 属性脱敏 ; 0x04: ⾥程限制 ;

0xFF: 其他

异常描述 128 STRING 对异常状态的具体描述，如“地理

围栏规则加载失败”。

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD-hh-mm-ss

数据包 ID 列表 N STRING 涉及泄露 / 丢失的数据包 ID，以逗

号分隔。

发⽣阶段 1 BYTE 0x01: 收集 , 0x02: 存储 , 0x03: 加

⼯ , 0x04: 传输 , 0x05: 提供 , 0x06:

公开

泄露 / 丢失原因 1 BYTE 0x01: 存储介质丢失 , 0x02: ⽹络攻

击 , 0x03: 内部⼈员违规 , 0x04: 配

置错误 , 0xFF: 其他

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD-hh-mm-ss

异常模块类型 1 BYTE 0x01: 地理围栏 ; 0x02: 图像识别 ;

0x03: 语义判定 ; 0x04: 去标识化 ;

0xFF: 其他

异常描述 128 STRING 对异常状态的具体描述，如“去标

识化裁切算法执⾏失败”。

0x12 权限越权事件数据格式

0x13 数据补发缓存池超限事件数据格式

0x14 平台异常登出事件

注：此事件由服务端在检测到客⼾端 TCP 连接异常中断时⽣成，客⼾端⽆需上报。

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD￾hh-mm-ss

越权账号 32 STRING 实施越权操作的账号

ID 或名称。

源 IP 地址 16 BYTE[16] 实施越权操作的源 IP

地址。

违规操作描述 128 STRING 对具体越权⾏为的描

述，如“尝试访问未

授权数据表”。

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

事件发⽣时间 6 BYTE[6] BCD 码 , YY-MM-DD￾hh-mm-ss

缓存池当前使⽤率 1 BYTE 当前缓存池已使⽤容

量的百分⽐，取值 0-

100。

缓存池容量阈值 1 BYTE 系统设定的缓存池容

量告警阈值，取值 0-

100。

超限时数据条数 4 DWORD 缓存池中待补发的数

据总条数。

6.6 控制指令

本命令（标识 0x83）为服务端向客⼾端下发控制类指令的统⼀格式。

7. 通信安全机制

7.1 ⾝份鉴别

客⼾端平台的⾝份鉴别通过平台登⼊（命令标识 0x10）流程完成。

备案与凭证获取 : 客⼾端平台在属地监测平台完成备案后，将获得⽤于平台登⼊的密码。其平

台统⼀社会信⽤代码将作为其唯⼀⾝份标识。

登⼊鉴权 : 客⼾端平台连接服务端平台后，必须⽴即发送平台登⼊消息。服务端平台通过验证

消息中的平台统⼀社会信⽤代码和平台密码的有效性，来确认客⼾端⾝份。

鉴权失败处理 : 鉴权失败时，服务端平台应通过应答消息返回具体的失败原因，并⽴即断开连

接。

7.2 数据加密

本协议通过消息头中的消息体属性字段来⽀持对数据单元的加密保护。

⽀持算法 : 本协议⽀持 SM2（⾮对称加密）、SM4（对称加密）等国家商⽤密码算法，以及

AES、RSA 等国际通⽤算法。

加密范围 : 加密操作仅针对数据包中的数据单元部分。

安全要求 : 涉及敏感信息、重要数据和核⼼数据的数据单元，必须采⽤加密⽅式进⾏传输。

7.3 数据校验

数据表⽰内容 ⻓度 / 字节 数据类型 描述及要求

控制类型 1 BYTE 0x01: 数据查询请求 ;

0x02: 远程参数配置 ;

0x03: 远程诊断 ...

控制参数⻓度 2 WORD 后续控制参数的总⻓度

控制参数 N BYTE[N] 根据“控制类型”不同，结构不同。

本协议通过数据包尾部的校验码字段来保证数据包在传输过程中的完整性。

校验算法 : 采⽤ CRC-16/CCITT-FALSE。

校验范围 : 从消息头的第⼀个字节开始，到消息体的最后⼀个字节。

加解密与校验顺序 : 当数据单元需要加密时，发送⽅应先对数据单元进⾏加密，然后对包含加

密消息体的报⽂计算 CRC 校验码。接收⽅在收到数据后，应先对报⽂进⾏ CRC 校验，校验通过

后再对消息体进⾏解密。

7.4 密钥管理

为保障对称加密算法（如 SM4）密钥的安全分发，本协议定义了密钥交换机制。

数据名称 英⽂简写 数据类型 ⻓度(字节) 含义

密钥类型 KEY_TYPE BYTE 1 0x01:SM2 公钥 ; 0x02:SM4

会话密钥

密钥⻓度 KEY_LENGTH WORD 2 密钥字段的总字节数

密钥 KEY BYTE[n] n 密钥内容。当交换 SM4 密

钥时，该密钥内容应使⽤对

⽅的 SM2 公钥进⾏加密保

护。

启⽤时间 ENABLE_TIM

E

STRING[13] 13 密钥启⽤时间 , UTC 时间戳

(毫秒级)

失效时间 EXPIRE_TIME STRING[13] 13 密钥失效时间 , UTC 时间戳

(毫秒级)

