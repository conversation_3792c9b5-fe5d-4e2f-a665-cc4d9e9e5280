{"version": 3, "sources": ["../../../src/components/tooltip.props.tsx"], "sourcesContent": ["import { widthPropDefs } from '../props/width.props.js';\n\nimport type { PropDef, GetPropDefTypes } from '../props/prop-def.js';\n\nconst tooltipPropDefs = {\n  content: { type: 'ReactNode', required: true },\n  width: widthPropDefs.width,\n  minWidth: widthPropDefs.minWidth,\n  maxWidth: { ...widthPropDefs.maxWidth, default: '360px' },\n} satisfies {\n  width: PropDef<string>;\n  minWidth: PropDef<string>;\n  maxWidth: PropDef<string>;\n  content: PropDef<React.ReactNode>;\n};\n\ntype TooltipOwnProps = GetPropDefTypes<typeof tooltipPropDefs & typeof widthPropDefs>;\n\nexport { tooltipPropDefs };\nexport type { TooltipOwnProps };\n"], "mappings": "AAAA,OAAS,iBAAAA,MAAqB,0BAI9B,MAAMC,EAAkB,CACtB,QAAS,CAAE,KAAM,YAAa,SAAU,EAAK,EAC7C,MAAOD,EAAc,MACrB,SAAUA,EAAc,SACxB,SAAU,CAAE,GAAGA,EAAc,SAAU,QAAS,OAAQ,CAC1D", "names": ["widthPropDefs", "tooltipPropDefs"]}