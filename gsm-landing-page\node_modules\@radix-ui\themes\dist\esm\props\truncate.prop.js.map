{"version": 3, "sources": ["../../../src/props/truncate.prop.ts"], "sourcesContent": ["import type { PropDef } from './prop-def.js';\n\nconst truncatePropDef = {\n  truncate: {\n    type: 'boolean',\n    className: 'rt-truncate',\n  },\n} satisfies {\n  truncate: PropDef<boolean>;\n};\n\nexport { truncatePropDef };\n"], "mappings": "AAEA,MAAMA,EAAkB,CACtB,SAAU,CACR,KAAM,UACN,UAAW,aACb,CACF", "names": ["truncatePropDef"]}