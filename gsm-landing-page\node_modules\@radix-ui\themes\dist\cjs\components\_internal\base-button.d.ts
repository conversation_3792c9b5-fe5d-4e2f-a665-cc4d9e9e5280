import * as React from 'react';
import { baseButtonPropDefs } from './base-button.props.js';
import type { MarginProps } from '../../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../../helpers/component-props.js';
import type { GetPropDefTypes } from '../../props/prop-def.js';
type BaseButtonOwnProps = GetPropDefTypes<typeof baseButtonPropDefs>;
interface BaseButtonProps extends ComponentPropsWithout<'button', RemovedProps>, MarginProps, BaseButtonOwnProps {
}
declare const BaseButton: React.ForwardRefExoticComponent<BaseButtonProps & React.RefAttributes<HTMLButtonElement>>;
export { BaseButton };
export type { BaseButtonProps };
//# sourceMappingURL=base-button.d.ts.map