import * as React from 'react';
import { separatorPropDefs } from './separator.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type SeparatorOwnProps = GetPropDefTypes<typeof separatorPropDefs>;
interface SeparatorProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, SeparatorOwnProps {
}
declare const Separator: React.ForwardRefExoticComponent<SeparatorProps & React.RefAttributes<HTMLSpanElement>>;
export { Separator };
export type { SeparatorProps };
//# sourceMappingURL=separator.d.ts.map