import type { baseButtonPropDefs } from '../components/_internal/base-button.props.js';
import type { calloutRootPropDefs } from '../components/callout.props.js';
import type { spinnerPropDefs } from '../components/spinner.props.js';
import type { textPropDefs } from '../components/text.props.js';
import type { Responsive } from '../props/prop-def.js';
declare function mapResponsiveProp<Input extends string, Output>(propValue: Responsive<Input> | undefined, mapValue: (value: Input) => Output): Responsive<Output> | undefined;
declare function mapCalloutSizeToTextSize(size: (typeof calloutRootPropDefs.size.values)[number]): (typeof textPropDefs.size.values)[number];
declare function mapButtonSizeToSpinnerSize(size: (typeof baseButtonPropDefs.size.values)[number]): (typeof spinnerPropDefs.size.values)[number];
export { mapResponsiveProp, mapCalloutSizeToTextSize, mapButtonSizeToSpinnerSize };
//# sourceMappingURL=map-prop-values.d.ts.map