import * as React from 'react';
import { textAreaPropDefs } from './text-area.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type TextAreaOwnProps = GetPropDefTypes<typeof textAreaPropDefs> & {
    defaultValue?: string;
    value?: string;
};
interface TextAreaProps extends ComponentPropsWithout<'textarea', RemovedProps | 'size' | 'value'>, MarginProps, TextAreaOwnProps {
}
declare const TextArea: React.ForwardRefExoticComponent<TextAreaProps & React.RefAttributes<HTMLTextAreaElement>>;
export { TextArea };
export type { TextAreaProps };
//# sourceMappingURL=text-area.d.ts.map