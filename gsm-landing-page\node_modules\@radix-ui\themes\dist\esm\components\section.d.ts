import * as React from 'react';
import type { LayoutProps } from '../props/layout.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { SectionOwnProps } from './section.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface SectionProps extends ComponentPropsWithout<'div', RemovedProps>, MarginProps, LayoutProps, SectionOwnProps {
}
declare const Section: React.ForwardRefExoticComponent<SectionProps & React.RefAttributes<HTMLDivElement>>;
export { Section };
export type { SectionProps };
//# sourceMappingURL=section.d.ts.map