<script setup lang="ts">
import { Theme } from '@radix-ui/themes'
</script>

<template>
  <Theme
    :accent-color="'blue'"
    :gray-color="'slate'"
    :radius="'medium'"
    :scaling="'100%'"
  >
    <router-view />
  </Theme>
</template>

<style>
:root {
  --accent-9: #409EFF;
  --accent-11: #409EFF;
  --gray-9: #909399;
  --gray-11: #909399;
}

[data-theme="dark"] {
  --accent-9: #3D63DD;
  --accent-11: #3D63DD;
  --gray-9: #8B8D98;
  --gray-11: #8B8D98;
  --color-background: #111111;
}

[data-theme="light"] {
  --color-background: #FFFFFF;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--color-background);
}
</style>
