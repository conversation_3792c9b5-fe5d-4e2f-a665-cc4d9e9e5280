import { Slot as SlotPrimitive } from 'radix-ui';
export declare const Root: import("react").ForwardRefExoticComponent<SlotPrimitive.SlotProps & import("react").RefAttributes<HTMLElement>>;
export declare const Slot: import("react").ForwardRefExoticComponent<SlotPrimitive.SlotProps & import("react").RefAttributes<HTMLElement>>;
export declare const Slottable: ({ children }: {
    children: React.ReactNode;
}) => import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=slot.d.ts.map