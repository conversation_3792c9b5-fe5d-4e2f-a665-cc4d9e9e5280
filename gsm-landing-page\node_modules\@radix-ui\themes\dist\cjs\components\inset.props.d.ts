declare const insetPropDefs: {
    side: {
        type: "enum";
        className: string;
        values: readonly ["all", "x", "y", "top", "bottom", "left", "right"];
        default: "all";
        responsive: true;
    };
    clip: {
        type: "enum";
        className: string;
        values: readonly ["border-box", "padding-box"];
        default: "border-box";
        responsive: true;
    };
    p: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    px: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    py: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    pt: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    pr: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    pb: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    pl: {
        type: "enum";
        className: string;
        values: readonly ["current", "0"];
        parseValue: typeof parsePaddingValue;
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
declare function parsePaddingValue(value: string): string;
export { insetPropDefs };
//# sourceMappingURL=inset.props.d.ts.map