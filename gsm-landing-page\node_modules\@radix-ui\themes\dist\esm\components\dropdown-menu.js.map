{"version": 3, "sources": ["../../../src/components/dropdown-menu.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { DropdownMenu as DropdownMenuPrimitive, Slot } from 'radix-ui';\n\nimport { ScrollArea } from './scroll-area.js';\nimport {\n  dropdownMenuContentPropDefs,\n  dropdownMenuItemPropDefs,\n  dropdownMenuCheckboxItemPropDefs,\n  dropdownMenuRadioItemPropDefs,\n} from './dropdown-menu.props.js';\nimport { Theme, useThemeContext } from './theme.js';\nimport { ChevronDownIcon, ThickCheckIcon, ThickChevronRightIcon } from './icons.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { requireReactElement } from '../helpers/require-react-element.js';\n\nimport type { IconProps } from './icons.js';\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ninterface DropdownMenuRootProps\n  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Root> {}\nconst DropdownMenuRoot: React.FC<DropdownMenuRootProps> = (props) => (\n  <DropdownMenuPrimitive.Root {...props} />\n);\nDropdownMenuRoot.displayName = 'DropdownMenu.Root';\n\ntype DropdownMenuTriggerElement = React.ElementRef<typeof DropdownMenuPrimitive.Trigger>;\ninterface DropdownMenuTriggerProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Trigger, RemovedProps> {}\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  ({ children, ...props }, forwardedRef) => (\n    <DropdownMenuPrimitive.Trigger {...props} ref={forwardedRef} asChild>\n      {requireReactElement(children)}\n    </DropdownMenuPrimitive.Trigger>\n  )\n);\nDropdownMenuTrigger.displayName = 'DropdownMenu.Trigger';\n\ntype DropdownMenuContentOwnProps = GetPropDefTypes<typeof dropdownMenuContentPropDefs>;\ntype DropdownMenuContentContextValue = DropdownMenuContentOwnProps;\nconst DropdownMenuContentContext = React.createContext<DropdownMenuContentContextValue>({});\ntype DropdownMenuContentElement = React.ElementRef<typeof DropdownMenuPrimitive.Content>;\ninterface DropdownMenuContentProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Content, RemovedProps>,\n    DropdownMenuContentContextValue {\n  container?: React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>['container'];\n}\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props, forwardedRef) => {\n    const themeContext = useThemeContext();\n    const {\n      size = dropdownMenuContentPropDefs.size.default,\n      variant = dropdownMenuContentPropDefs.variant.default,\n      highContrast = dropdownMenuContentPropDefs.highContrast.default,\n    } = props;\n    const { className, children, color, container, forceMount, ...contentProps } = extractProps(\n      props,\n      dropdownMenuContentPropDefs\n    );\n    const resolvedColor = color || themeContext.accentColor;\n    return (\n      <DropdownMenuPrimitive.Portal container={container} forceMount={forceMount}>\n        <Theme asChild>\n          <DropdownMenuPrimitive.Content\n            data-accent-color={resolvedColor}\n            align=\"start\"\n            sideOffset={4}\n            collisionPadding={10}\n            {...contentProps}\n            asChild={false}\n            ref={forwardedRef}\n            className={classNames(\n              'rt-PopperContent',\n              'rt-BaseMenuContent',\n              'rt-DropdownMenuContent',\n              className\n            )}\n          >\n            <ScrollArea type=\"auto\">\n              <div className={classNames('rt-BaseMenuViewport', 'rt-DropdownMenuViewport')}>\n                <DropdownMenuContentContext.Provider\n                  value={React.useMemo(\n                    () => ({ size, variant, color: resolvedColor, highContrast }),\n                    [size, variant, resolvedColor, highContrast]\n                  )}\n                >\n                  {children}\n                </DropdownMenuContentContext.Provider>\n              </div>\n            </ScrollArea>\n          </DropdownMenuPrimitive.Content>\n        </Theme>\n      </DropdownMenuPrimitive.Portal>\n    );\n  }\n);\nDropdownMenuContent.displayName = 'DropdownMenu.Content';\n\ntype DropdownMenuLabelElement = React.ElementRef<typeof DropdownMenuPrimitive.Label>;\ninterface DropdownMenuLabelProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Label, RemovedProps> {}\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <DropdownMenuPrimitive.Label\n      {...props}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-BaseMenuLabel', 'rt-DropdownMenuLabel', className)}\n    />\n  )\n);\nDropdownMenuLabel.displayName = 'DropdownMenu.Label';\n\ntype DropdownMenuItemElement = React.ElementRef<typeof DropdownMenuPrimitive.Item>;\ntype DropdownMenuItemOwnProps = GetPropDefTypes<typeof dropdownMenuItemPropDefs>;\ninterface DropdownMenuItemProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Item, RemovedProps>,\n    DropdownMenuItemOwnProps {}\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props, forwardedRef) => {\n    const {\n      className,\n      children,\n      color = dropdownMenuItemPropDefs.color.default,\n      shortcut,\n      ...itemProps\n    } = props;\n    return (\n      <DropdownMenuPrimitive.Item\n        data-accent-color={color}\n        {...itemProps}\n        ref={forwardedRef}\n        className={classNames('rt-reset', 'rt-BaseMenuItem', 'rt-DropdownMenuItem', className)}\n      >\n        <Slot.Slottable>{children}</Slot.Slottable>\n        {shortcut && <div className=\"rt-BaseMenuShortcut rt-DropdownMenuShortcut\">{shortcut}</div>}\n      </DropdownMenuPrimitive.Item>\n    );\n  }\n);\nDropdownMenuItem.displayName = 'DropdownMenu.Item';\n\ntype DropdownMenuGroupElement = React.ElementRef<typeof DropdownMenuPrimitive.Group>;\ninterface DropdownMenuGroupProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Group, RemovedProps> {}\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  ({ className, ...props }, forwardedRef) => (\n    <DropdownMenuPrimitive.Group\n      {...props}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames('rt-BaseMenuGroup', 'rt-DropdownMenuGroup', className)}\n    />\n  )\n);\nDropdownMenuGroup.displayName = 'DropdownMenu.Group';\n\ntype DropdownMenuRadioGroupElement = React.ElementRef<typeof DropdownMenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.RadioGroup, RemovedProps> {}\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>(({ className, ...props }, forwardedRef) => (\n  <DropdownMenuPrimitive.RadioGroup\n    {...props}\n    asChild={false}\n    ref={forwardedRef}\n    className={classNames('rt-BaseMenuRadioGroup', 'rt-DropdownMenuRadioGroup', className)}\n  />\n));\nDropdownMenuRadioGroup.displayName = 'DropdownMenu.RadioGroup';\n\ntype DropdownMenuRadioItemElement = React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>;\ntype DropdownMenuRadioItemOwnProps = GetPropDefTypes<typeof dropdownMenuRadioItemPropDefs>;\ninterface DropdownMenuRadioItemProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.RadioItem, RemovedProps>,\n    DropdownMenuRadioItemOwnProps {}\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    color = dropdownMenuRadioItemPropDefs.color.default,\n    ...itemProps\n  } = props;\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      {...itemProps}\n      asChild={false}\n      ref={forwardedRef}\n      data-accent-color={color}\n      className={classNames(\n        'rt-BaseMenuItem',\n        'rt-BaseMenuRadioItem',\n        'rt-DropdownMenuItem',\n        'rt-DropdownMenuRadioItem',\n        className\n      )}\n    >\n      {children}\n      <DropdownMenuPrimitive.ItemIndicator className=\"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator\">\n        <ThickCheckIcon className=\"rt-BaseMenuItemIndicatorIcon rt-DropdownMenuItemIndicatorIcon\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </DropdownMenuPrimitive.RadioItem>\n  );\n});\nDropdownMenuRadioItem.displayName = 'DropdownMenu.RadioItem';\n\ntype DropdownMenuCheckboxItemElement = React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>;\ntype DropdownMenuCheckboxItemOwnProps = GetPropDefTypes<typeof dropdownMenuCheckboxItemPropDefs>;\ninterface DropdownMenuCheckboxItemProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.CheckboxItem, RemovedProps>,\n    DropdownMenuCheckboxItemOwnProps {}\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    shortcut,\n    color = dropdownMenuCheckboxItemPropDefs.color.default,\n    ...itemProps\n  } = props;\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      {...itemProps}\n      asChild={false}\n      ref={forwardedRef}\n      data-accent-color={color}\n      className={classNames(\n        'rt-BaseMenuItem',\n        'rt-BaseMenuCheckboxItem',\n        'rt-DropdownMenuItem',\n        'rt-DropdownMenuCheckboxItem',\n        className\n      )}\n    >\n      {children}\n      <DropdownMenuPrimitive.ItemIndicator className=\"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator\">\n        <ThickCheckIcon className=\"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n      {shortcut && <div className=\"rt-BaseMenuShortcut rt-DropdownMenuShortcut\">{shortcut}</div>}\n    </DropdownMenuPrimitive.CheckboxItem>\n  );\n});\nDropdownMenuCheckboxItem.displayName = 'DropdownMenu.CheckboxItem';\n\ninterface DropdownMenuSubProps\n  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Sub> {}\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (props) => (\n  <DropdownMenuPrimitive.Sub {...props} />\n);\nDropdownMenuSub.displayName = 'DropdownMenu.Sub';\n\ntype DropdownMenuSubTriggerElement = React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.SubTrigger, RemovedProps> {}\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props, forwardedRef) => {\n  const { className, children, ...subTriggerProps } = props;\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      {...subTriggerProps}\n      asChild={false}\n      ref={forwardedRef}\n      className={classNames(\n        'rt-BaseMenuItem',\n        'rt-BaseMenuSubTrigger',\n        'rt-DropdownMenuItem',\n        'rt-DropdownMenuSubTrigger',\n        className\n      )}\n    >\n      {children}\n      <div className=\"rt-BaseMenuShortcut rt-DropdownMenuShortcut\">\n        <ThickChevronRightIcon className=\"rt-BaseMenuSubTriggerIcon rt-DropdownMenuSubtriggerIcon\" />\n      </div>\n    </DropdownMenuPrimitive.SubTrigger>\n  );\n});\nDropdownMenuSubTrigger.displayName = 'DropdownMenu.SubTrigger';\n\ntype DropdownMenuSubContentElement = React.ElementRef<typeof DropdownMenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.SubContent, RemovedProps> {\n  container?: React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>['container'];\n}\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props, forwardedRef) => {\n  const { size, variant, color, highContrast } = React.useContext(DropdownMenuContentContext);\n  const { className, children, container, forceMount, ...subContentProps } = extractProps(\n    { size, variant, color, highContrast, ...props },\n    dropdownMenuContentPropDefs\n  );\n  return (\n    <DropdownMenuPrimitive.Portal container={container} forceMount={forceMount}>\n      <Theme asChild>\n        <DropdownMenuPrimitive.SubContent\n          data-accent-color={color}\n          alignOffset={-Number(size) * 4}\n          // Side offset accounts for the outer solid box-shadow\n          sideOffset={1}\n          collisionPadding={10}\n          {...subContentProps}\n          asChild={false}\n          ref={forwardedRef}\n          className={classNames(\n            'rt-PopperContent',\n            'rt-BaseMenuContent',\n            'rt-BaseMenuSubContent',\n            'rt-DropdownMenuContent',\n            'rt-DropdownMenuSubContent',\n            className\n          )}\n        >\n          <ScrollArea type=\"auto\">\n            <div className={classNames('rt-BaseMenuViewport', 'rt-DropdownMenuViewport')}>\n              {children}\n            </div>\n          </ScrollArea>\n        </DropdownMenuPrimitive.SubContent>\n      </Theme>\n    </DropdownMenuPrimitive.Portal>\n  );\n});\nDropdownMenuSubContent.displayName = 'DropdownMenu.SubContent';\n\ntype DropdownMenuSeparatorElement = React.ElementRef<typeof DropdownMenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps\n  extends ComponentPropsWithout<typeof DropdownMenuPrimitive.Separator, RemovedProps> {}\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>(({ className, ...props }, forwardedRef) => (\n  <DropdownMenuPrimitive.Separator\n    {...props}\n    asChild={false}\n    ref={forwardedRef}\n    className={classNames('rt-BaseMenuSeparator', 'rt-DropdownMenuSeparator', className)}\n  />\n));\nDropdownMenuSeparator.displayName = 'DropdownMenu.Separator';\n\nexport {\n  DropdownMenuRoot as Root,\n  DropdownMenuTrigger as Trigger,\n  ChevronDownIcon as TriggerIcon,\n  DropdownMenuContent as Content,\n  DropdownMenuLabel as Label,\n  DropdownMenuItem as Item,\n  DropdownMenuGroup as Group,\n  DropdownMenuRadioGroup as RadioGroup,\n  DropdownMenuRadioItem as RadioItem,\n  DropdownMenuCheckboxItem as CheckboxItem,\n  DropdownMenuSub as Sub,\n  DropdownMenuSubTrigger as SubTrigger,\n  DropdownMenuSubContent as SubContent,\n  DropdownMenuSeparator as Separator,\n};\n\nexport type {\n  DropdownMenuRootProps as RootProps,\n  DropdownMenuTriggerProps as TriggerProps,\n  IconProps as TriggerIconProps,\n  DropdownMenuContentProps as ContentProps,\n  DropdownMenuLabelProps as LabelProps,\n  DropdownMenuItemProps as ItemProps,\n  DropdownMenuGroupProps as GroupProps,\n  DropdownMenuRadioGroupProps as RadioGroupProps,\n  DropdownMenuRadioItemProps as RadioItemProps,\n  DropdownMenuCheckboxItemProps as CheckboxItemProps,\n  DropdownMenuSubProps as SubProps,\n  DropdownMenuSubTriggerProps as SubTriggerProps,\n  DropdownMenuSubContentProps as SubContentProps,\n  DropdownMenuSeparatorProps as SeparatorProps,\n};\n"], "mappings": "aAEA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,gBAAgBC,EAAuB,QAAAC,MAAY,WAE5D,OAAS,cAAAC,MAAkB,mBAC3B,OACE,+BAAAC,EACA,4BAAAC,EACA,oCAAAC,EACA,iCAAAC,MACK,2BACP,OAAS,SAAAC,EAAO,mBAAAC,MAAuB,aACvC,OAAS,mBAAAC,EAAiB,kBAAAC,EAAgB,yBAAAC,MAA6B,aACvE,OAAS,gBAAAC,MAAoB,8BAC7B,OAAS,uBAAAC,MAA2B,sCAQpC,MAAMC,EAAqDC,GACzDjB,EAAA,cAACE,EAAsB,KAAtB,CAA4B,GAAGe,EAAO,EAEzCD,EAAiB,YAAc,oBAK/B,MAAME,EAAsBlB,EAAM,WAChC,CAAC,CAAE,SAAAmB,EAAU,GAAGF,CAAM,EAAGG,IACvBpB,EAAA,cAACE,EAAsB,QAAtB,CAA+B,GAAGe,EAAO,IAAKG,EAAc,QAAO,IACjEL,EAAoBI,CAAQ,CAC/B,CAEJ,EACAD,EAAoB,YAAc,uBAIlC,MAAMG,EAA6BrB,EAAM,cAA+C,CAAC,CAAC,EAOpFsB,EAAsBtB,EAAM,WAChC,CAACiB,EAAOG,IAAiB,CACvB,MAAMG,EAAeb,EAAgB,EAC/B,CACJ,KAAAc,EAAOnB,EAA4B,KAAK,QACxC,QAAAoB,EAAUpB,EAA4B,QAAQ,QAC9C,aAAAqB,EAAerB,EAA4B,aAAa,OAC1D,EAAIY,EACE,CAAE,UAAAU,EAAW,SAAAR,EAAU,MAAAS,EAAO,UAAAC,EAAW,WAAAC,EAAY,GAAGC,CAAa,EAAIjB,EAC7EG,EACAZ,CACF,EACM2B,EAAgBJ,GAASL,EAAa,YAC5C,OACEvB,EAAA,cAACE,EAAsB,OAAtB,CAA6B,UAAW2B,EAAW,WAAYC,GAC9D9B,EAAA,cAACS,EAAA,CAAM,QAAO,IACZT,EAAA,cAACE,EAAsB,QAAtB,CACC,oBAAmB8B,EACnB,MAAM,QACN,WAAY,EACZ,iBAAkB,GACjB,GAAGD,EACJ,QAAS,GACT,IAAKX,EACL,UAAWnB,EACT,mBACA,qBACA,yBACA0B,CACF,GAEA3B,EAAA,cAACI,EAAA,CAAW,KAAK,QACfJ,EAAA,cAAC,OAAI,UAAWC,EAAW,sBAAuB,yBAAyB,GACzED,EAAA,cAACqB,EAA2B,SAA3B,CACC,MAAOrB,EAAM,QACX,KAAO,CAAE,KAAAwB,EAAM,QAAAC,EAAS,MAAOO,EAAe,aAAAN,CAAa,GAC3D,CAACF,EAAMC,EAASO,EAAeN,CAAY,CAC7C,GAECP,CACH,CACF,CACF,CACF,CACF,CACF,CAEJ,CACF,EACAG,EAAoB,YAAc,uBAKlC,MAAMW,EAAoBjC,EAAM,WAC9B,CAAC,CAAE,UAAA2B,EAAW,GAAGV,CAAM,EAAGG,IACxBpB,EAAA,cAACE,EAAsB,MAAtB,CACE,GAAGe,EACJ,QAAS,GACT,IAAKG,EACL,UAAWnB,EAAW,mBAAoB,uBAAwB0B,CAAS,EAC7E,CAEJ,EACAM,EAAkB,YAAc,qBAOhC,MAAMC,EAAmBlC,EAAM,WAC7B,CAACiB,EAAOG,IAAiB,CACvB,KAAM,CACJ,UAAAO,EACA,SAAAR,EACA,MAAAS,EAAQtB,EAAyB,MAAM,QACvC,SAAA6B,EACA,GAAGC,CACL,EAAInB,EACJ,OACEjB,EAAA,cAACE,EAAsB,KAAtB,CACC,oBAAmB0B,EAClB,GAAGQ,EACJ,IAAKhB,EACL,UAAWnB,EAAW,WAAY,kBAAmB,sBAAuB0B,CAAS,GAErF3B,EAAA,cAACG,EAAK,UAAL,KAAgBgB,CAAS,EACzBgB,GAAYnC,EAAA,cAAC,OAAI,UAAU,+CAA+CmC,CAAS,CACtF,CAEJ,CACF,EACAD,EAAiB,YAAc,oBAK/B,MAAMG,EAAoBrC,EAAM,WAC9B,CAAC,CAAE,UAAA2B,EAAW,GAAGV,CAAM,EAAGG,IACxBpB,EAAA,cAACE,EAAsB,MAAtB,CACE,GAAGe,EACJ,QAAS,GACT,IAAKG,EACL,UAAWnB,EAAW,mBAAoB,uBAAwB0B,CAAS,EAC7E,CAEJ,EACAU,EAAkB,YAAc,qBAKhC,MAAMC,EAAyBtC,EAAM,WAGnC,CAAC,CAAE,UAAA2B,EAAW,GAAGV,CAAM,EAAGG,IAC1BpB,EAAA,cAACE,EAAsB,WAAtB,CACE,GAAGe,EACJ,QAAS,GACT,IAAKG,EACL,UAAWnB,EAAW,wBAAyB,4BAA6B0B,CAAS,EACvF,CACD,EACDW,EAAuB,YAAc,0BAOrC,MAAMC,EAAwBvC,EAAM,WAGlC,CAACiB,EAAOG,IAAiB,CACzB,KAAM,CACJ,SAAAD,EACA,UAAAQ,EACA,MAAAC,EAAQpB,EAA8B,MAAM,QAC5C,GAAG4B,CACL,EAAInB,EACJ,OACEjB,EAAA,cAACE,EAAsB,UAAtB,CACE,GAAGkC,EACJ,QAAS,GACT,IAAKhB,EACL,oBAAmBQ,EACnB,UAAW3B,EACT,kBACA,uBACA,sBACA,2BACA0B,CACF,GAECR,EACDnB,EAAA,cAACE,EAAsB,cAAtB,CAAoC,UAAU,yDAC7CF,EAAA,cAACY,EAAA,CAAe,UAAU,gEAAgE,CAC5F,CACF,CAEJ,CAAC,EACD2B,EAAsB,YAAc,yBAOpC,MAAMC,EAA2BxC,EAAM,WAGrC,CAACiB,EAAOG,IAAiB,CACzB,KAAM,CACJ,SAAAD,EACA,UAAAQ,EACA,SAAAQ,EACA,MAAAP,EAAQrB,EAAiC,MAAM,QAC/C,GAAG6B,CACL,EAAInB,EACJ,OACEjB,EAAA,cAACE,EAAsB,aAAtB,CACE,GAAGkC,EACJ,QAAS,GACT,IAAKhB,EACL,oBAAmBQ,EACnB,UAAW3B,EACT,kBACA,0BACA,sBACA,8BACA0B,CACF,GAECR,EACDnB,EAAA,cAACE,EAAsB,cAAtB,CAAoC,UAAU,yDAC7CF,EAAA,cAACY,EAAA,CAAe,UAAU,+DAA+D,CAC3F,EACCuB,GAAYnC,EAAA,cAAC,OAAI,UAAU,+CAA+CmC,CAAS,CACtF,CAEJ,CAAC,EACDK,EAAyB,YAAc,4BAIvC,MAAMC,EAAmDxB,GACvDjB,EAAA,cAACE,EAAsB,IAAtB,CAA2B,GAAGe,EAAO,EAExCwB,EAAgB,YAAc,mBAK9B,MAAMC,EAAyB1C,EAAM,WAGnC,CAACiB,EAAOG,IAAiB,CACzB,KAAM,CAAE,UAAAO,EAAW,SAAAR,EAAU,GAAGwB,CAAgB,EAAI1B,EACpD,OACEjB,EAAA,cAACE,EAAsB,WAAtB,CACE,GAAGyC,EACJ,QAAS,GACT,IAAKvB,EACL,UAAWnB,EACT,kBACA,wBACA,sBACA,4BACA0B,CACF,GAECR,EACDnB,EAAA,cAAC,OAAI,UAAU,+CACbA,EAAA,cAACa,EAAA,CAAsB,UAAU,0DAA0D,CAC7F,CACF,CAEJ,CAAC,EACD6B,EAAuB,YAAc,0BAOrC,MAAME,EAAyB5C,EAAM,WAGnC,CAACiB,EAAOG,IAAiB,CACzB,KAAM,CAAE,KAAAI,EAAM,QAAAC,EAAS,MAAAG,EAAO,aAAAF,CAAa,EAAI1B,EAAM,WAAWqB,CAA0B,EACpF,CAAE,UAAAM,EAAW,SAAAR,EAAU,UAAAU,EAAW,WAAAC,EAAY,GAAGe,CAAgB,EAAI/B,EACzE,CAAE,KAAAU,EAAM,QAAAC,EAAS,MAAAG,EAAO,aAAAF,EAAc,GAAGT,CAAM,EAC/CZ,CACF,EACA,OACEL,EAAA,cAACE,EAAsB,OAAtB,CAA6B,UAAW2B,EAAW,WAAYC,GAC9D9B,EAAA,cAACS,EAAA,CAAM,QAAO,IACZT,EAAA,cAACE,EAAsB,WAAtB,CACC,oBAAmB0B,EACnB,YAAa,CAAC,OAAOJ,CAAI,EAAI,EAE7B,WAAY,EACZ,iBAAkB,GACjB,GAAGqB,EACJ,QAAS,GACT,IAAKzB,EACL,UAAWnB,EACT,mBACA,qBACA,wBACA,yBACA,4BACA0B,CACF,GAEA3B,EAAA,cAACI,EAAA,CAAW,KAAK,QACfJ,EAAA,cAAC,OAAI,UAAWC,EAAW,sBAAuB,yBAAyB,GACxEkB,CACH,CACF,CACF,CACF,CACF,CAEJ,CAAC,EACDyB,EAAuB,YAAc,0BAKrC,MAAME,EAAwB9C,EAAM,WAGlC,CAAC,CAAE,UAAA2B,EAAW,GAAGV,CAAM,EAAGG,IAC1BpB,EAAA,cAACE,EAAsB,UAAtB,CACE,GAAGe,EACJ,QAAS,GACT,IAAKG,EACL,UAAWnB,EAAW,uBAAwB,2BAA4B0B,CAAS,EACrF,CACD,EACDmB,EAAsB,YAAc", "names": ["React", "classNames", "DropdownMenuPrimitive", "Slot", "ScrollArea", "dropdownMenuContentPropDefs", "dropdownMenuItemPropDefs", "dropdownMenuCheckboxItemPropDefs", "dropdownMenuRadioItemPropDefs", "Theme", "useThemeContext", "ChevronDownIcon", "ThickCheckIcon", "ThickChevronRightIcon", "extractProps", "requireReactElement", "DropdownMenuRoot", "props", "DropdownMenuTrigger", "children", "forwardedRef", "DropdownMenuContentContext", "DropdownMenuContent", "themeContext", "size", "variant", "highContrast", "className", "color", "container", "forceMount", "contentProps", "resolvedColor", "DropdownMenuLabel", "DropdownMenuItem", "shortcut", "itemProps", "DropdownMenuGroup", "DropdownMenuRadioGroup", "DropdownMenuRadioItem", "DropdownMenuCheckboxItem", "DropdownMenuSub", "DropdownMenuSubTrigger", "subTriggerProps", "DropdownMenuSubContent", "subContentProps", "DropdownMenuSeparator"]}