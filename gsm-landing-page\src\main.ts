import '@radix-ui/themes/styles.css'
import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import LandingPage from './components/LandingPage.vue'

const routes = [
  { path: '/', component: LandingPage },
  { path: '/login/government', name: 'government-login', component: () => import('./components/LoginPage.vue'), props: { userType: 'government' } },
  { path: '/login/enterprise', name: 'enterprise-login', component: () => import('./components/LoginPage.vue'), props: { userType: 'enterprise' } }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()

createApp(App)
  .use(pinia)
  .use(router)
  .mount('#app')
