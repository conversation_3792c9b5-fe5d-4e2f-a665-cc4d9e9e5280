export {
  default as <PERSON>boxGroupRoot,
  type CheckboxGroupRootEmits,
  type CheckboxGroupRootProps,
  injectCheckboxGroupRootContext,
} from './CheckboxGroupRoot.vue'
export {
  default as CheckboxIndicator,
  type CheckboxIndicatorProps,
} from './CheckboxIndicator.vue'
export {
  default as CheckboxRoot,
  type CheckboxRootEmits,
  type CheckboxRootProps,
  injectCheckboxRootContext,
} from './CheckboxRoot.vue'
export type {
  CheckedState as CheckboxCheckedState,
} from './utils'
