import { asChildPropDef } from '../props/as-child.prop.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const containerPropDefs: {
    /**
     * Controls the **max-width** of the content within the container.
     * Supports the predefined values and responsive objects.
     *
     * @values
     * | Size     | Max. width |
     * | :------- | ---------: |
     * | size="1" | 448px      |
     * | size="2" | 688px      |
     * | size="3" | 880px      |
     * | size="4" | 1136px     |
     *
     * @example
     * size="4"
     * size={{ sm: '3', lg: '4' }}
     *
     * @link
     * https://github.com/radix-ui/themes/blob/main/packages/radix-ui-themes/src/components/container.css
     */
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4"];
        default: "4";
        responsive: true;
    };
    /**
     * Controls whether the container is visible or hidden.
     * Supports "none", "initial", and responsive object values.
     *
     * @example
     * display="none"
     * display={{ sm: 'none', lg: 'initial' }}
     */
    display: {
        type: "enum";
        className: string;
        values: readonly ["none", "initial"];
        parseValue: typeof parseDisplayValue;
        responsive: true;
    };
    /**
     * Controls the alignment of the content within the container.
     *
     * @example
     * align="center"
     * align={{ initial: 'left', lg: 'center' }}
     */
    align: {
        type: "enum";
        className: string;
        values: readonly ["left", "center", "right"];
        parseValue: typeof parseAlignValue;
        responsive: true;
    };
    asChild: {
        type: "boolean";
    };
};
declare function parseDisplayValue(value: string): string;
declare function parseAlignValue(value: string): string;
type ContainerOwnProps = GetPropDefTypes<typeof containerPropDefs & typeof asChildPropDef>;
export { containerPropDefs };
export type { ContainerOwnProps };
//# sourceMappingURL=container.props.d.ts.map