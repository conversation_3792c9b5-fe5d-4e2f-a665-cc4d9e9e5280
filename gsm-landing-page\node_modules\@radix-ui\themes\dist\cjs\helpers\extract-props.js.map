{"version": 3, "sources": ["../../../src/helpers/extract-props.ts"], "sourcesContent": ["import classNames from 'classnames';\n\nimport { getResponsiveClassNames, getResponsiveStyles } from './get-responsive-styles.js';\nimport { isResponsiveObject } from './is-responsive-object.js';\nimport { mergeStyles } from './merge-styles.js';\n\nimport type * as React from 'react';\nimport type { PropDef } from '../props/prop-def.js';\n\ntype PropDefsWithClassName<T> = T extends Record<string, PropDef>\n  ? { [K in keyof T]: T[K] extends { className: string } ? K : never }[keyof T]\n  : never;\n\nfunction mergePropDefs<T extends Record<string, PropDef>[]>(...args: T): Record<string, PropDef> {\n  return Object.assign({}, ...args);\n}\n\n/**\n * Takes props, checks them against prop defs that have a `className` on them,\n * adds necessary CSS classes and inline styles, and returns the props without\n * the corresponding prop defs that were used to formulate the new `className`\n * and `style` values. Also applies prop def defaults to every prop.\n */\nfunction extractProps<\n  P extends { className?: string; style?: React.CSSProperties; [key: string]: any },\n  T extends Record<string, PropDef>[]\n>(\n  props: P,\n  ...propDefs: T\n): Omit<P & { className?: string; style?: React.CSSProperties }, PropDefsWithClassName<T[number]>> {\n  let className: string | undefined;\n  let style: ReturnType<typeof mergeStyles>;\n  const extractedProps = { ...props };\n  const allPropDefs = mergePropDefs(...propDefs);\n\n  for (const key in allPropDefs) {\n    let value = extractedProps[key];\n    const propDef = allPropDefs[key];\n\n    // Apply prop def defaults\n    if (propDef.default !== undefined && value === undefined) {\n      value = propDef.default;\n    }\n\n    // Apply the default value if the value is not a valid enum value\n    if (propDef.type === 'enum') {\n      const values = [propDef.default, ...propDef.values];\n\n      if (!values.includes(value) && !isResponsiveObject(value)) {\n        value = propDef.default;\n      }\n    }\n\n    // Apply the value with defaults\n    (extractedProps as Record<string, any>)[key] = value;\n\n    if ('className' in propDef && propDef.className) {\n      delete extractedProps[key];\n\n      const isResponsivePropDef = 'responsive' in propDef;\n      // Make sure we are not threading through responsive values for non-responsive prop defs\n      if (!value || (isResponsiveObject(value) && !isResponsivePropDef)) {\n        continue;\n      }\n\n      if (isResponsiveObject(value)) {\n        // Apply prop def defaults to the `initial` breakpoint\n        if (propDef.default !== undefined && value.initial === undefined) {\n          value.initial = propDef.default;\n        }\n\n        // Apply the default value to the `initial` breakpoint when it is not a valid enum value\n        if (propDef.type === 'enum') {\n          const values = [propDef.default, ...propDef.values];\n\n          if (!values.includes(value.initial)) {\n            value.initial = propDef.default;\n          }\n        }\n      }\n\n      if (propDef.type === 'enum') {\n        const propClassName = getResponsiveClassNames({\n          allowArbitraryValues: false,\n          value,\n          className: propDef.className,\n          propValues: propDef.values,\n          parseValue: propDef.parseValue,\n        });\n\n        className = classNames(className, propClassName);\n        continue;\n      }\n\n      if (propDef.type === 'string' || propDef.type === 'enum | string') {\n        const propDefValues = propDef.type === 'string' ? [] : propDef.values;\n\n        const [propClassNames, propCustomProperties] = getResponsiveStyles({\n          className: propDef.className,\n          customProperties: propDef.customProperties,\n          propValues: propDefValues,\n          parseValue: propDef.parseValue,\n          value,\n        });\n\n        style = mergeStyles(style, propCustomProperties);\n        className = classNames(className, propClassNames);\n        continue;\n      }\n\n      if (propDef.type === 'boolean' && value) {\n        // TODO handle responsive boolean props\n        className = classNames(className, propDef.className);\n        continue;\n      }\n    }\n  }\n\n  extractedProps.className = classNames(className, props.className);\n  extractedProps.style = mergeStyles(style, props.style);\n  return extractedProps;\n}\n\nexport { extractProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,kBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,yBAEvBC,EAA6D,sCAC7DC,EAAmC,qCACnCC,EAA4B,6BAS5B,SAASC,KAAsDC,EAAkC,CAC/F,OAAO,OAAO,OAAO,CAAC,EAAG,GAAGA,CAAI,CAClC,CAQA,SAASP,EAIPQ,KACGC,EAC8F,CACjG,IAAIC,EACAC,EACJ,MAAMC,EAAiB,CAAE,GAAGJ,CAAM,EAC5BK,EAAcP,EAAc,GAAGG,CAAQ,EAE7C,UAAWK,KAAOD,EAAa,CAC7B,IAAIE,EAAQH,EAAeE,CAAG,EAC9B,MAAME,EAAUH,EAAYC,CAAG,EAmB/B,GAhBIE,EAAQ,UAAY,QAAaD,IAAU,SAC7CA,EAAQC,EAAQ,SAIdA,EAAQ,OAAS,QAGf,CAFW,CAACA,EAAQ,QAAS,GAAGA,EAAQ,MAAM,EAEtC,SAASD,CAAK,GAAK,IAAC,sBAAmBA,CAAK,IACtDA,EAAQC,EAAQ,SAKnBJ,EAAuCE,CAAG,EAAIC,EAE3C,cAAeC,GAAWA,EAAQ,UAAW,CAC/C,OAAOJ,EAAeE,CAAG,EAEzB,MAAMG,EAAsB,eAAgBD,EAE5C,GAAI,CAACD,MAAU,sBAAmBA,CAAK,GAAK,CAACE,EAC3C,SAmBF,MAhBI,sBAAmBF,CAAK,IAEtBC,EAAQ,UAAY,QAAaD,EAAM,UAAY,SACrDA,EAAM,QAAUC,EAAQ,SAItBA,EAAQ,OAAS,SACJ,CAACA,EAAQ,QAAS,GAAGA,EAAQ,MAAM,EAEtC,SAASD,EAAM,OAAO,IAChCA,EAAM,QAAUC,EAAQ,WAK1BA,EAAQ,OAAS,OAAQ,CAC3B,MAAME,KAAgB,2BAAwB,CAC5C,qBAAsB,GACtB,MAAAH,EACA,UAAWC,EAAQ,UACnB,WAAYA,EAAQ,OACpB,WAAYA,EAAQ,UACtB,CAAC,EAEDN,KAAY,EAAAS,SAAWT,EAAWQ,CAAa,EAC/C,QACF,CAEA,GAAIF,EAAQ,OAAS,UAAYA,EAAQ,OAAS,gBAAiB,CACjE,MAAMI,EAAgBJ,EAAQ,OAAS,SAAW,CAAC,EAAIA,EAAQ,OAEzD,CAACK,EAAgBC,CAAoB,KAAI,uBAAoB,CACjE,UAAWN,EAAQ,UACnB,iBAAkBA,EAAQ,iBAC1B,WAAYI,EACZ,WAAYJ,EAAQ,WACpB,MAAAD,CACF,CAAC,EAEDJ,KAAQ,eAAYA,EAAOW,CAAoB,EAC/CZ,KAAY,EAAAS,SAAWT,EAAWW,CAAc,EAChD,QACF,CAEA,GAAIL,EAAQ,OAAS,WAAaD,EAAO,CAEvCL,KAAY,EAAAS,SAAWT,EAAWM,EAAQ,SAAS,EACnD,QACF,CACF,CACF,CAEA,OAAAJ,EAAe,aAAY,EAAAO,SAAWT,EAAWF,EAAM,SAAS,EAChEI,EAAe,SAAQ,eAAYD,EAAOH,EAAM,KAAK,EAC9CI,CACT", "names": ["extract_props_exports", "__export", "extractProps", "__toCommonJS", "import_classnames", "import_get_responsive_styles", "import_is_responsive_object", "import_merge_styles", "mergePropDefs", "args", "props", "propDefs", "className", "style", "extractedProps", "allPropDefs", "key", "value", "propDef", "isResponsivePropDef", "propClassName", "classNames", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propClassNames", "propCustomProperties"]}