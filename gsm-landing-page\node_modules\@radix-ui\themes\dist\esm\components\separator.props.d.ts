declare const separatorPropDefs: {
    orientation: {
        type: "enum";
        className: string;
        values: readonly ["horizontal", "vertical"];
        default: "horizontal";
        responsive: true;
    };
    size: {
        type: "enum";
        className: string;
        values: readonly ["1", "2", "3", "4"];
        default: "1";
        responsive: true;
    };
    color: {
        default: "gray";
        type: "enum";
        values: readonly ["gray", "gold", "bronze", "brown", "yellow", "amber", "orange", "tomato", "red", "ruby", "crimson", "pink", "plum", "purple", "violet", "iris", "indigo", "blue", "cyan", "teal", "jade", "green", "grass", "lime", "mint", "sky"];
    };
    decorative: {
        type: "boolean";
        default: true;
    };
};
export { separatorPropDefs };
//# sourceMappingURL=separator.props.d.ts.map