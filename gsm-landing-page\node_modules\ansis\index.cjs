let e,t,r,{defineProperty:n,setPrototypeOf:l,create:o,keys:s}=Object,i="",{round:c,max:a}=Math,p=e=>{let t=/([a-f\d]{3,6})/i.exec(e)?.[1],r=t?.length,n=parseInt(6^r?3^r?"0":t[0]+t[0]+t[1]+t[1]+t[2]+t[2]:t,16);return[n>>16&255,n>>8&255,255&n]},u=(e,t,r)=>e^t||t^r?16+36*c(e/51)+6*c(t/51)+c(r/51):8>e?16:e>248?231:c(24*(e-8)/247)+232,d=e=>{let t,r,n,l,o;return 8>e?30+e:16>e?e-8+90:(232>e?(o=(e-=16)%36,t=(e/36|0)/5,r=(o/6|0)/5,n=o%6/5):t=r=n=(10*(e-232)+8)/255,l=2*a(t,r,n),l?30+(c(n)<<2|c(r)<<1|c(t))+(2^l?0:60):30)},g=(()=>{let r=e=>o.some((t=>e.test(t))),n=globalThis,l=n.process??{},o=l.argv??[],i=l.env??{},c=-1;try{e=","+s(i).join(",")}catch(e){i={},c=0}let a="FORCE_COLOR",p={false:0,0:0,1:1,2:2,3:3}[i[a]]??-1,u=a in i&&p||r(/^--color=?(true|always)?$/);return u&&(c=p),~c||(c=((r,n,l)=>(t=r.TERM,{"24bit":3,truecolor:3,ansi256:2,ansi:1}[r.COLORTERM]||(r.CI?/,GITHUB/.test(e)?3:1:n&&"dumb"!==t?l?3:/-256/.test(t)?2:1:0)))(i,!!i.PM2_HOME||i.NEXT_RUNTIME?.includes("edge")||!!l.stdout?.isTTY,"win32"===l.platform)),!p||i.NO_COLOR||r(/^--(no-color|color=(false|never))$/)?0:n.window?.chrome||u&&!c?3:c})(),f={open:i,close:i},h=39,b=49,O={},m=({p:e},{open:t,close:n})=>{let o=(e,...r)=>{if(!e){if(t&&t===n)return t;if((e??i)===i)return i}let l,s=e.raw?String.raw({raw:e},...r):i+e,c=o.p,a=c.o,p=c.c;if(s.includes(""))for(;c;c=c.p){let{open:e,close:t}=c,r=t.length,n=i,o=0;if(r)for(;~(l=s.indexOf(t,o));o=l+r)n+=s.slice(o,l)+e;s=n+s.slice(o)}return a+(s.includes("\n")?s.replace(/(\r?\n)/g,p+"$1"+a):s)+p},s=t,c=n;return e&&(s=e.o+t,c=n+e.c),l(o,r),o.p={open:t,close:n,o:s,c,p:e},o.open=s,o.close=c,o};const w=function(e=g){let t={Ansis:w,level:e,isSupported:()=>s,strip:e=>e.replace(/[][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g,i),extend(e){for(let t in e){let r=e[t],l=(typeof r)[0],o="s"===l?x(...p(r)):r;O[t]="f"===l?{get(){return(...e)=>m(this,r(...e))}}:{get(){let e=m(this,o);return n(this,t,{value:e}),e}}}return r=o({},O),l(t,r),t}},s=e>0,c=(e,t)=>s?{open:`[${e}m`,close:`[${t}m`}:f,a=e=>t=>e(...p(t)),y=(e,t)=>(r,n,l)=>c(`${e}8;2;${r};${n};${l}`,t),R=(e,t)=>(r,n,l)=>c(((e,t,r)=>d(u(e,t,r)))(r,n,l)+e,t),$=e=>(t,r,n)=>e(u(t,r,n)),x=y(3,h),T=y(4,b),v=e=>c("38;5;"+e,h),C=e=>c("48;5;"+e,b);2===e?(x=$(v),T=$(C)):1===e&&(x=R(0,h),T=R(10,b),v=e=>c(d(e),h),C=e=>c(d(e)+10,b));let E,M={fg:v,bg:C,rgb:x,bgRgb:T,hex:a(x),bgHex:a(T),visible:f,reset:c(0,0),bold:c(1,22),dim:c(2,22),italic:c(3,23),underline:c(4,24),inverse:c(7,27),hidden:c(8,28),strikethrough:c(9,29)},I="Bright";return"black,red,green,yellow,blue,magenta,cyan,white,gray".split(",").map(((e,t)=>{E="bg"+e[0].toUpperCase()+e.slice(1),8>t?(M[e+I]=c(90+t,h),M[E+I]=c(100+t,b)):t=60,M[e]=c(30+t,h),M[E]=c(40+t,b)})),t.extend(M)},y=new w;module.exports=y,y.default=y;
