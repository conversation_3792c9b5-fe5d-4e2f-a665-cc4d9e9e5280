{"version": 3, "sources": ["../src/index.ts", "../src/password-toggle-field.tsx"], "sourcesContent": ["'use client';\nexport type {\n  PasswordToggleFieldProps,\n  PasswordToggleFieldInputProps,\n  PasswordToggleFieldToggleProps,\n  PasswordToggleFieldIconProps,\n  PasswordToggleFieldSlotProps,\n} from './password-toggle-field';\nexport {\n  PasswordToggleField,\n  PasswordToggleFieldInput,\n  PasswordToggleFieldToggle,\n  PasswordToggleFieldIcon,\n  PasswordToggleFieldSlot,\n  //\n  Root,\n  Input,\n  Toggle,\n  Icon,\n  Slot,\n} from './password-toggle-field';\n", "import * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useId } from '@radix-ui/react-id';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\nimport type { Scope } from '@radix-ui/react-context';\nimport { createContextScope } from '@radix-ui/react-context';\n\nconst PASSWORD_TOGGLE_FIELD_NAME = 'PasswordToggleField';\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldProvider\n * -----------------------------------------------------------------------------------------------*/\n\ntype InternalFocusState = {\n  clickTriggered: boolean;\n  selectionStart: number | null;\n  selectionEnd: number | null;\n};\n\ninterface PasswordToggleFieldContextValue {\n  inputId: string;\n  inputRef: React.RefObject<HTMLInputElement | null>;\n  visible: boolean;\n  setVisible: React.Dispatch<React.SetStateAction<boolean>>;\n  syncInputId: (providedId: string | number | undefined) => void;\n  focusState: React.RefObject<InternalFocusState>;\n}\n\nconst [createPasswordToggleFieldContext] = createContextScope(PASSWORD_TOGGLE_FIELD_NAME);\nconst [PasswordToggleFieldProvider, usePasswordToggleFieldContext] =\n  createPasswordToggleFieldContext<PasswordToggleFieldContextValue>(PASSWORD_TOGGLE_FIELD_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleField\n * -----------------------------------------------------------------------------------------------*/\n\ntype ScopedProps<P> = P & { __scopePasswordToggleField?: Scope };\n\ninterface PasswordToggleFieldProps {\n  id?: string;\n  visible?: boolean;\n  defaultVisible?: boolean;\n  onVisiblityChange?: (visible: boolean) => void;\n  children?: React.ReactNode;\n}\n\nconst INITIAL_FOCUS_STATE: InternalFocusState = {\n  clickTriggered: false,\n  selectionStart: null,\n  selectionEnd: null,\n};\n\nconst PasswordToggleField: React.FC<PasswordToggleFieldProps> = ({\n  __scopePasswordToggleField,\n  ...props\n}: ScopedProps<PasswordToggleFieldProps>) => {\n  const baseId = useId(props.id);\n  const defaultInputId = `${baseId}-input`;\n  const [inputIdState, setInputIdState] = React.useState<null | string>(defaultInputId);\n  const inputId = inputIdState ?? defaultInputId;\n  const syncInputId = React.useCallback(\n    (providedId: string | number | undefined) =>\n      setInputIdState(providedId != null ? String(providedId) : null),\n    []\n  );\n\n  const { visible: visibleProp, defaultVisible, onVisiblityChange, children } = props;\n  const [visible = false, setVisible] = useControllableState({\n    caller: PASSWORD_TOGGLE_FIELD_NAME,\n    prop: visibleProp,\n    defaultProp: defaultVisible ?? false,\n    onChange: onVisiblityChange,\n  });\n\n  const inputRef = React.useRef<HTMLInputElement | null>(null);\n  const focusState = React.useRef<InternalFocusState>(INITIAL_FOCUS_STATE);\n\n  return (\n    <PasswordToggleFieldProvider\n      scope={__scopePasswordToggleField}\n      inputId={inputId}\n      inputRef={inputRef}\n      setVisible={setVisible}\n      syncInputId={syncInputId}\n      visible={visible}\n      focusState={focusState}\n    >\n      {children}\n    </PasswordToggleFieldProvider>\n  );\n};\nPasswordToggleField.displayName = PASSWORD_TOGGLE_FIELD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_INPUT_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Input';\n\ntype PrimitiveInputProps = React.ComponentPropsWithoutRef<'input'>;\n\ninterface PasswordToggleFieldOwnProps {\n  autoComplete?: 'current-password' | 'new-password';\n}\n\ninterface PasswordToggleFieldInputProps\n  extends PasswordToggleFieldOwnProps,\n    Omit<PrimitiveInputProps, keyof PasswordToggleFieldOwnProps | 'type'> {\n  autoComplete?: 'current-password' | 'new-password';\n}\n\nconst PasswordToggleFieldInput = React.forwardRef<HTMLInputElement, PasswordToggleFieldInputProps>(\n  (\n    {\n      __scopePasswordToggleField,\n      autoComplete = 'current-password',\n      autoCapitalize = 'off',\n      spellCheck = false,\n      id: idProp,\n      ...props\n    }: ScopedProps<PasswordToggleFieldInputProps>,\n    forwardedRef\n  ) => {\n    const { visible, inputRef, inputId, syncInputId, setVisible, focusState } =\n      usePasswordToggleFieldContext(PASSWORD_TOGGLE_FIELD_INPUT_NAME, __scopePasswordToggleField);\n\n    React.useEffect(() => {\n      syncInputId(idProp);\n    }, [idProp, syncInputId]);\n\n    // We want to reset the visibility to `false` to revert the input to\n    // `type=\"password\"` when:\n    // - The form is reset (for consistency with other form controls)\n    // - The form is submitted (to prevent the browser from remembering the\n    //   input's value.\n    //\n    // See \"Keeping things secure\":\n    //   https://technology.blog.gov.uk/2021/04/19/simple-things-are-complicated-making-a-show-password-option/)\n    const _setVisible = useEffectEvent(setVisible);\n    React.useEffect(() => {\n      const inputElement = inputRef.current;\n      const form = inputElement?.form;\n      if (!form) {\n        return;\n      }\n\n      const controller = new AbortController();\n      form.addEventListener(\n        'reset',\n        (event) => {\n          if (!event.defaultPrevented) {\n            _setVisible(false);\n          }\n        },\n        { signal: controller.signal }\n      );\n      form.addEventListener(\n        'submit',\n        () => {\n          // always reset the visibility on submit regardless of whether the\n          // default action is prevented\n          _setVisible(false);\n        },\n        { signal: controller.signal }\n      );\n      return () => {\n        controller.abort();\n      };\n    }, [inputRef, _setVisible]);\n\n    return (\n      <Primitive.input\n        {...props}\n        id={idProp ?? inputId}\n        autoCapitalize={autoCapitalize}\n        autoComplete={autoComplete}\n        ref={useComposedRefs(forwardedRef, inputRef)}\n        spellCheck={spellCheck}\n        type={visible ? 'text' : 'password'}\n        onBlur={composeEventHandlers(props.onBlur, (event) => {\n          // get the cursor position\n          const { selectionStart, selectionEnd } = event.currentTarget;\n          focusState.current.selectionStart = selectionStart;\n          focusState.current.selectionEnd = selectionEnd;\n        })}\n      />\n    );\n  }\n);\nPasswordToggleFieldInput.displayName = PASSWORD_TOGGLE_FIELD_INPUT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldToggle\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_TOGGLE_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Toggle';\n\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<'button'>;\n\ninterface PasswordToggleFieldToggleProps extends Omit<PrimitiveButtonProps, 'type'> {}\n\nconst PasswordToggleFieldToggle = React.forwardRef<\n  HTMLButtonElement,\n  PasswordToggleFieldToggleProps\n>(\n  (\n    {\n      __scopePasswordToggleField,\n      onClick,\n      onPointerDown,\n      onPointerCancel,\n      onPointerUp,\n      onFocus,\n      children,\n      'aria-label': ariaLabelProp,\n      'aria-controls': ariaControls,\n      'aria-hidden': ariaHidden,\n      tabIndex,\n      ...props\n    }: ScopedProps<PasswordToggleFieldToggleProps>,\n    forwardedRef\n  ) => {\n    const { setVisible, visible, inputRef, inputId, focusState } = usePasswordToggleFieldContext(\n      PASSWORD_TOGGLE_FIELD_TOGGLE_NAME,\n      __scopePasswordToggleField\n    );\n    const [internalAriaLabel, setInternalAriaLabel] = React.useState<string | undefined>(undefined);\n    const elementRef = React.useRef<HTMLButtonElement>(null);\n    const ref = useComposedRefs(forwardedRef, elementRef);\n    const isHydrated = useIsHydrated();\n\n    React.useEffect(() => {\n      const element = elementRef.current;\n      if (!element || ariaLabelProp) {\n        setInternalAriaLabel(undefined);\n        return;\n      }\n\n      const DEFAULT_ARIA_LABEL = visible ? 'Hide password' : 'Show password';\n\n      function checkForInnerTextLabel(textContent: string | undefined | null) {\n        const text = textContent ? textContent : undefined;\n        // If the element has inner text, no need to force an aria-label.\n        setInternalAriaLabel(text ? undefined : DEFAULT_ARIA_LABEL);\n      }\n\n      checkForInnerTextLabel(element.textContent);\n\n      const observer = new MutationObserver((entries) => {\n        let textContent: string | undefined;\n        for (const entry of entries) {\n          if (entry.type === 'characterData') {\n            if (element.textContent) {\n              textContent = element.textContent;\n            }\n          }\n        }\n        checkForInnerTextLabel(textContent);\n      });\n      observer.observe(element, { characterData: true, subtree: true });\n      return () => {\n        observer.disconnect();\n      };\n    }, [visible, ariaLabelProp]);\n\n    const ariaLabel = ariaLabelProp || internalAriaLabel;\n\n    // Before hydration the button will not work, but we want to render it\n    // regardless to prevent potential layout shift. Hide it from assistive tech\n    // by default. Post-hydration it will be visible, focusable and associated\n    // with the input via aria-controls.\n    if (!isHydrated) {\n      ariaHidden ??= true;\n      tabIndex ??= -1;\n    } else {\n      ariaControls ??= inputId;\n    }\n\n    React.useEffect(() => {\n      let cleanup = () => {};\n      const ownerWindow = elementRef.current?.ownerDocument?.defaultView || window;\n      const reset = () => (focusState.current.clickTriggered = false);\n      const handlePointerUp = () => (cleanup = requestIdleCallback(ownerWindow, reset));\n      ownerWindow.addEventListener('pointerup', handlePointerUp);\n      return () => {\n        cleanup();\n        ownerWindow.removeEventListener('pointerup', handlePointerUp);\n      };\n    }, [focusState]);\n\n    return (\n      <Primitive.button\n        aria-controls={ariaControls}\n        aria-hidden={ariaHidden}\n        aria-label={ariaLabel}\n        ref={ref}\n        id={inputId}\n        {...props}\n        onPointerDown={composeEventHandlers(onPointerDown, () => {\n          focusState.current.clickTriggered = true;\n        })}\n        onPointerCancel={(event) => {\n          // do not use `composeEventHandlers` here because we always want to\n          // reset the ref on cancellation, regardless of whether the user has\n          // called preventDefault on the event\n          onPointerCancel?.(event);\n          focusState.current = INITIAL_FOCUS_STATE;\n        }}\n        // do not use `composeEventHandlers` here because we always want to\n        // reset the ref after click, regardless of whether the user has\n        // called preventDefault on the event\n        onClick={(event) => {\n          onClick?.(event);\n          if (event.defaultPrevented) {\n            focusState.current = INITIAL_FOCUS_STATE;\n            return;\n          }\n\n          flushSync(() => {\n            setVisible((s) => !s);\n          });\n          if (focusState.current.clickTriggered) {\n            const input = inputRef.current;\n            if (input) {\n              const { selectionStart, selectionEnd } = focusState.current;\n              input.focus();\n              if (selectionStart !== null || selectionEnd !== null) {\n                // wait a tick so that focus has settled, then restore select position\n                requestAnimationFrame(() => {\n                  // make sure the input still has focus (developer may have\n                  // programatically moved focus elsewhere)\n                  if (input.ownerDocument.activeElement === input) {\n                    input.selectionStart = selectionStart;\n                    input.selectionEnd = selectionEnd;\n                  }\n                });\n              }\n            }\n          }\n          focusState.current = INITIAL_FOCUS_STATE;\n        }}\n        onPointerUp={(event) => {\n          onPointerUp?.(event);\n          // if click handler hasn't been called at this point, it may have been\n          // intercepted, in which case we still want to reset our internal\n          // state\n          setTimeout(() => {\n            focusState.current = INITIAL_FOCUS_STATE;\n          }, 50);\n        }}\n        type=\"button\"\n      >\n        {children}\n      </Primitive.button>\n    );\n  }\n);\nPasswordToggleFieldToggle.displayName = PASSWORD_TOGGLE_FIELD_TOGGLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldSlot\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_SLOT_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Slot';\n\ninterface PasswordToggleFieldSlotDeclarativeProps {\n  visible: React.ReactNode;\n  hidden: React.ReactNode;\n}\n\ninterface PasswordToggleFieldSlotRenderProps {\n  render: (args: { visible: boolean }) => React.ReactElement;\n}\n\ntype PasswordToggleFieldSlotProps =\n  | PasswordToggleFieldSlotDeclarativeProps\n  | PasswordToggleFieldSlotRenderProps;\n\nconst PasswordToggleFieldSlot: React.FC<PasswordToggleFieldSlotProps> = ({\n  __scopePasswordToggleField,\n  ...props\n}: ScopedProps<PasswordToggleFieldSlotProps>) => {\n  const { visible } = usePasswordToggleFieldContext(\n    PASSWORD_TOGGLE_FIELD_SLOT_NAME,\n    __scopePasswordToggleField\n  );\n\n  return 'render' in props\n    ? //\n      props.render({ visible })\n    : visible\n      ? props.visible\n      : props.hidden;\n};\nPasswordToggleFieldSlot.displayName = PASSWORD_TOGGLE_FIELD_SLOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_ICON_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Icon';\n\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<'svg'>;\n\ninterface PasswordToggleFieldIconProps extends Omit<PrimitiveSvgProps, 'children'> {\n  visible: React.ReactElement;\n  hidden: React.ReactElement;\n}\n\nconst PasswordToggleFieldIcon = React.forwardRef<SVGSVGElement, PasswordToggleFieldIconProps>(\n  (\n    {\n      __scopePasswordToggleField,\n      // @ts-expect-error\n      children,\n      ...props\n    }: ScopedProps<PasswordToggleFieldIconProps>,\n    forwardedRef\n  ) => {\n    const { visible } = usePasswordToggleFieldContext(\n      PASSWORD_TOGGLE_FIELD_ICON_NAME,\n      __scopePasswordToggleField\n    );\n    const { visible: visibleIcon, hidden: hiddenIcon, ...domProps } = props;\n    return (\n      <Primitive.svg {...domProps} ref={forwardedRef} aria-hidden asChild>\n        {visible ? visibleIcon : hiddenIcon}\n      </Primitive.svg>\n    );\n  }\n);\nPasswordToggleFieldIcon.displayName = PASSWORD_TOGGLE_FIELD_ICON_NAME;\n\nexport {\n  PasswordToggleField,\n  PasswordToggleFieldInput,\n  PasswordToggleFieldToggle,\n  PasswordToggleFieldSlot,\n  PasswordToggleFieldIcon,\n  //\n  PasswordToggleField as Root,\n  PasswordToggleFieldInput as Input,\n  PasswordToggleFieldToggle as Toggle,\n  PasswordToggleFieldSlot as Slot,\n  PasswordToggleFieldIcon as Icon,\n};\nexport type {\n  PasswordToggleFieldProps,\n  PasswordToggleFieldInputProps,\n  PasswordToggleFieldToggleProps,\n  PasswordToggleFieldIconProps,\n  PasswordToggleFieldSlotProps,\n};\n\nfunction requestIdleCallback(\n  window: Window,\n  callback: IdleRequestCallback,\n  options?: IdleRequestOptions\n): () => void {\n  if ((window as any).requestIdleCallback) {\n    const id = window.requestIdleCallback(callback, options);\n    return () => {\n      window.cancelIdleCallback(id);\n    };\n  }\n  const start = Date.now();\n  const id = window.setTimeout(() => {\n    const timeRemaining = () => Math.max(0, 50 - (Date.now() - start));\n    callback({ didTimeout: false, timeRemaining });\n  }, 1);\n  return () => {\n    window.clearTimeout(id);\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,uBAA0B;AAC1B,uBAAqC;AACrC,0CAAqC;AACrC,6BAA0B;AAC1B,gCAAgC;AAChC,sBAAsB;AACtB,mCAA8B;AAC9B,oCAA+B;AAE/B,2BAAmC;AAyE/B;AAvEJ,IAAM,6BAA6B;AAqBnC,IAAM,CAAC,gCAAgC,QAAI,yCAAmB,0BAA0B;AACxF,IAAM,CAAC,6BAA6B,6BAA6B,IAC/D,iCAAkE,0BAA0B;AAgB9F,IAAM,sBAA0C;AAAA,EAC9C,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAChB;AAEA,IAAM,sBAA0D,CAAC;AAAA,EAC/D;AAAA,EACA,GAAG;AACL,MAA6C;AAC3C,QAAM,aAAS,uBAAM,MAAM,EAAE;AAC7B,QAAM,iBAAiB,GAAG,MAAM;AAChC,QAAM,CAAC,cAAc,eAAe,IAAU,eAAwB,cAAc;AACpF,QAAM,UAAU,gBAAgB;AAChC,QAAM,cAAoB;AAAA,IACxB,CAAC,eACC,gBAAgB,cAAc,OAAO,OAAO,UAAU,IAAI,IAAI;AAAA,IAChE,CAAC;AAAA,EACH;AAEA,QAAM,EAAE,SAAS,aAAa,gBAAgB,mBAAmB,SAAS,IAAI;AAC9E,QAAM,CAAC,UAAU,OAAO,UAAU,QAAI,0DAAqB;AAAA,IACzD,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,aAAa,kBAAkB;AAAA,IAC/B,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,WAAiB,aAAgC,IAAI;AAC3D,QAAM,aAAmB,aAA2B,mBAAmB;AAEvE,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;AACA,oBAAoB,cAAc;AAMlC,IAAM,mCAAmC,6BAA6B;AActE,IAAM,2BAAiC;AAAA,EACrC,CACE;AAAA,IACE;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,IAAI;AAAA,IACJ,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,EAAE,SAAS,UAAU,SAAS,aAAa,YAAY,WAAW,IACtE,8BAA8B,kCAAkC,0BAA0B;AAE5F,IAAM,gBAAU,MAAM;AACpB,kBAAY,MAAM;AAAA,IACpB,GAAG,CAAC,QAAQ,WAAW,CAAC;AAUxB,UAAM,kBAAc,8CAAe,UAAU;AAC7C,IAAM,gBAAU,MAAM;AACpB,YAAM,eAAe,SAAS;AAC9B,YAAM,OAAO,cAAc;AAC3B,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AAEA,YAAM,aAAa,IAAI,gBAAgB;AACvC,WAAK;AAAA,QACH;AAAA,QACA,CAAC,UAAU;AACT,cAAI,CAAC,MAAM,kBAAkB;AAC3B,wBAAY,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,EAAE,QAAQ,WAAW,OAAO;AAAA,MAC9B;AACA,WAAK;AAAA,QACH;AAAA,QACA,MAAM;AAGJ,sBAAY,KAAK;AAAA,QACnB;AAAA,QACA,EAAE,QAAQ,WAAW,OAAO;AAAA,MAC9B;AACA,aAAO,MAAM;AACX,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF,GAAG,CAAC,UAAU,WAAW,CAAC;AAE1B,WACE;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACE,GAAG;AAAA,QACJ,IAAI,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,SAAK,2CAAgB,cAAc,QAAQ;AAAA,QAC3C;AAAA,QACA,MAAM,UAAU,SAAS;AAAA,QACzB,YAAQ,uCAAqB,MAAM,QAAQ,CAAC,UAAU;AAEpD,gBAAM,EAAE,gBAAgB,aAAa,IAAI,MAAM;AAC/C,qBAAW,QAAQ,iBAAiB;AACpC,qBAAW,QAAQ,eAAe;AAAA,QACpC,CAAC;AAAA;AAAA,IACH;AAAA,EAEJ;AACF;AACA,yBAAyB,cAAc;AAMvC,IAAM,oCAAoC,6BAA6B;AAMvE,IAAM,4BAAkC;AAAA,EAItC,CACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf;AAAA,IACA,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,EAAE,YAAY,SAAS,UAAU,SAAS,WAAW,IAAI;AAAA,MAC7D;AAAA,MACA;AAAA,IACF;AACA,UAAM,CAAC,mBAAmB,oBAAoB,IAAU,eAA6B,MAAS;AAC9F,UAAM,aAAmB,aAA0B,IAAI;AACvD,UAAM,UAAM,2CAAgB,cAAc,UAAU;AACpD,UAAM,iBAAa,4CAAc;AAEjC,IAAM,gBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,CAAC,WAAW,eAAe;AAC7B,6BAAqB,MAAS;AAC9B;AAAA,MACF;AAEA,YAAM,qBAAqB,UAAU,kBAAkB;AAEvD,eAAS,uBAAuB,aAAwC;AACtE,cAAM,OAAO,cAAc,cAAc;AAEzC,6BAAqB,OAAO,SAAY,kBAAkB;AAAA,MAC5D;AAEA,6BAAuB,QAAQ,WAAW;AAE1C,YAAM,WAAW,IAAI,iBAAiB,CAAC,YAAY;AACjD,YAAI;AACJ,mBAAW,SAAS,SAAS;AAC3B,cAAI,MAAM,SAAS,iBAAiB;AAClC,gBAAI,QAAQ,aAAa;AACvB,4BAAc,QAAQ;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AACA,+BAAuB,WAAW;AAAA,MACpC,CAAC;AACD,eAAS,QAAQ,SAAS,EAAE,eAAe,MAAM,SAAS,KAAK,CAAC;AAChE,aAAO,MAAM;AACX,iBAAS,WAAW;AAAA,MACtB;AAAA,IACF,GAAG,CAAC,SAAS,aAAa,CAAC;AAE3B,UAAM,YAAY,iBAAiB;AAMnC,QAAI,CAAC,YAAY;AACf,qBAAe;AACf,mBAAa;AAAA,IACf,OAAO;AACL,uBAAiB;AAAA,IACnB;AAEA,IAAM,gBAAU,MAAM;AACpB,UAAI,UAAU,MAAM;AAAA,MAAC;AACrB,YAAM,cAAc,WAAW,SAAS,eAAe,eAAe;AACtE,YAAM,QAAQ,MAAO,WAAW,QAAQ,iBAAiB;AACzD,YAAM,kBAAkB,MAAO,UAAU,oBAAoB,aAAa,KAAK;AAC/E,kBAAY,iBAAiB,aAAa,eAAe;AACzD,aAAO,MAAM;AACX,gBAAQ;AACR,oBAAY,oBAAoB,aAAa,eAAe;AAAA,MAC9D;AAAA,IACF,GAAG,CAAC,UAAU,CAAC;AAEf,WACE;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,iBAAe;AAAA,QACf,eAAa;AAAA,QACb,cAAY;AAAA,QACZ;AAAA,QACA,IAAI;AAAA,QACH,GAAG;AAAA,QACJ,mBAAe,uCAAqB,eAAe,MAAM;AACvD,qBAAW,QAAQ,iBAAiB;AAAA,QACtC,CAAC;AAAA,QACD,iBAAiB,CAAC,UAAU;AAI1B,4BAAkB,KAAK;AACvB,qBAAW,UAAU;AAAA,QACvB;AAAA,QAIA,SAAS,CAAC,UAAU;AAClB,oBAAU,KAAK;AACf,cAAI,MAAM,kBAAkB;AAC1B,uBAAW,UAAU;AACrB;AAAA,UACF;AAEA,0CAAU,MAAM;AACd,uBAAW,CAAC,MAAM,CAAC,CAAC;AAAA,UACtB,CAAC;AACD,cAAI,WAAW,QAAQ,gBAAgB;AACrC,kBAAM,QAAQ,SAAS;AACvB,gBAAI,OAAO;AACT,oBAAM,EAAE,gBAAgB,aAAa,IAAI,WAAW;AACpD,oBAAM,MAAM;AACZ,kBAAI,mBAAmB,QAAQ,iBAAiB,MAAM;AAEpD,sCAAsB,MAAM;AAG1B,sBAAI,MAAM,cAAc,kBAAkB,OAAO;AAC/C,0BAAM,iBAAiB;AACvB,0BAAM,eAAe;AAAA,kBACvB;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AACA,qBAAW,UAAU;AAAA,QACvB;AAAA,QACA,aAAa,CAAC,UAAU;AACtB,wBAAc,KAAK;AAInB,qBAAW,MAAM;AACf,uBAAW,UAAU;AAAA,UACvB,GAAG,EAAE;AAAA,QACP;AAAA,QACA,MAAK;AAAA,QAEJ;AAAA;AAAA,IACH;AAAA,EAEJ;AACF;AACA,0BAA0B,cAAc;AAMxC,IAAM,kCAAkC,6BAA6B;AAerE,IAAM,0BAAkE,CAAC;AAAA,EACvE;AAAA,EACA,GAAG;AACL,MAAiD;AAC/C,QAAM,EAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,EACF;AAEA,SAAO,YAAY;AAAA;AAAA,IAEf,MAAM,OAAO,EAAE,QAAQ,CAAC;AAAA,MACxB,UACE,MAAM,UACN,MAAM;AACd;AACA,wBAAwB,cAAc;AAMtC,IAAM,kCAAkC,6BAA6B;AASrE,IAAM,0BAAgC;AAAA,EACpC,CACE;AAAA,IACE;AAAA;AAAA,IAEA;AAAA,IACA,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,EAAE,QAAQ,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,UAAM,EAAE,SAAS,aAAa,QAAQ,YAAY,GAAG,SAAS,IAAI;AAClE,WACE,4CAAC,iCAAU,KAAV,EAAe,GAAG,UAAU,KAAK,cAAc,eAAW,MAAC,SAAO,MAChE,oBAAU,cAAc,YAC3B;AAAA,EAEJ;AACF;AACA,wBAAwB,cAAc;AAuBtC,SAAS,oBACPA,SACA,UACA,SACY;AACZ,MAAKA,QAAe,qBAAqB;AACvC,UAAMC,MAAKD,QAAO,oBAAoB,UAAU,OAAO;AACvD,WAAO,MAAM;AACX,MAAAA,QAAO,mBAAmBC,GAAE;AAAA,IAC9B;AAAA,EACF;AACA,QAAM,QAAQ,KAAK,IAAI;AACvB,QAAM,KAAKD,QAAO,WAAW,MAAM;AACjC,UAAM,gBAAgB,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM;AACjE,aAAS,EAAE,YAAY,OAAO,cAAc,CAAC;AAAA,EAC/C,GAAG,CAAC;AACJ,SAAO,MAAM;AACX,IAAAA,QAAO,aAAa,EAAE;AAAA,EACxB;AACF;", "names": ["window", "id"]}