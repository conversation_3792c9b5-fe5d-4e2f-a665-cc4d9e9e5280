import * as React from 'react';
import { headingPropDefs } from './heading.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type HeadingOwnProps = GetPropDefTypes<typeof headingPropDefs>;
interface HeadingProps extends ComponentPropsWithout<'h1', RemovedProps>, MarginProps, HeadingOwnProps {
}
declare const Heading: React.ForwardRefExoticComponent<HeadingProps & React.RefAttributes<HTMLHeadingElement>>;
export { Heading };
export type { HeadingProps };
//# sourceMappingURL=heading.d.ts.map