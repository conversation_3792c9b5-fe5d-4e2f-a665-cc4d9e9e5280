(function(){var e={2939:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-lf_fullscreen",use:"icon-lf_fullscreen-usage",viewBox:"0 0 16 16",content:'<symbol viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-lf_fullscreen">\n    <title>icon_full</title>\n    <g id="icon-lf_fullscreen_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-lf_fullscreen_仿真与故障恢复建议_工况列表_查看详情备份" transform="translate(-1608.000000, -1006.000000)">\n            <g id="icon-lf_fullscreen_编组" transform="translate(1592.000000, 998.000000)">\n                <g id="icon-lf_fullscreen_icon_full" transform="translate(16.000000, 8.000000)">\n                    <rect id="icon-lf_fullscreen_矩形备份-5" x="0" y="0" width="16" height="16" />\n                    <path d="M2,11 L2,13 C2,13.5128358 2.38604019,13.9355072 2.88337887,13.9932723 L3,14 L5,14 L5,15 L3,15 C1.8954305,15 1,14.1045695 1,13 L1,11 L2,11 Z M15,11 L15,13 C15,14.1045695 14.1045695,15 13,15 L11,15 L11,14 L13,14 C13.5128358,14 13.9355072,13.6139598 13.9932723,13.1166211 L14,13 L14,11 L15,11 Z M11,4 C11.5522847,4 12,4.44771525 12,5 L12,11 C12,11.5522847 11.5522847,12 11,12 L5,12 C4.44771525,12 4,11.5522847 4,11 L4,5 C4,4.44771525 4.44771525,4 5,4 L11,4 Z M11,5 L5,5 L5,11 L11,11 L11,5 Z M13,1 C14.1045695,1 15,1.8954305 15,3 L15,5 L14,5 L14,3 C14,2.48716416 13.6139598,2.06449284 13.1166211,2.00672773 L13,2 L11,2 L11,1 L13,1 Z M5,1 L5,2 L3,2 C2.48716416,2 2.06449284,2.38604019 2.00672773,2.88337887 L2,3 L2,5 L1,5 L1,3 C1,1.8954305 1.8954305,1 3,1 L5,1 Z" id="icon-lf_fullscreen_形状结合" fill="#515866" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},3255:function(e,t,n){"use strict";var i=n(5638),o=n(55787);const a=(0,i.nY)("route",()=>{let e=(0,o.Kh)({list:[]}),t=(0,o.Kh)({list:[]});function n(t){e.list=t}function i(e){t.list=e}return e.list=[],t.list=[],{routes:e,allRoutes:t,setRoutes:n,setAllRoutes:i}});t.A=a},7641:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_setting",use:"icon-icon_setting-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_setting"><path d="M588.8 128l12 83.2 4.8 34.4 31.2 14.4c12.8 6.4 26.4 13.6 38.4 21.6l28 18.4 31.2-12 81.6-32 76 127.2-67.2 51.2-28 21.6 3.2 35.2c0.8 7.2 0.8 14.4 0.8 20.8s0 13.6-0.8 20.8l-3.2 35.2 28 21.6 67.2 51.2-75.2 127.2-82.4-32-31.2-12-28 18.4c-12.8 8.8-25.6 16-38.4 21.6l-31.2 14.4-4.8 33.6-12 84H435.2l-12-83.2-4.8-34.4-31.2-14.4c-12.8-6.4-26.4-13.6-38.4-21.6l-28-18.4-31.2 12L208 768l-76-127.2 67.2-51.2 28-21.6-3.2-35.2c-0.8-7.2-0.8-14.4-0.8-20.8s0-13.6 0.8-20.8l3.2-35.2-28-21.6-67.2-51.2L207.2 256l82.4 32 31.2 12 28-18.4c12.8-8.8 25.6-16 38.4-21.6l31.2-14.4 4.8-33.6L435.2 128h153.6m8.8-64H426.4c-27.2 0-49.6 19.2-53.6 44.8L360 201.6c-16 7.2-31.2 16-47.2 26.4l-90.4-35.2c-6.4-2.4-12.8-3.2-19.2-3.2-19.2 0-37.6 9.6-46.4 26.4L71.2 360c-13.6 22.4-8 52 12.8 68l76 57.6c-0.8 9.6-1.6 18.4-1.6 26.4s0 16.8 1.6 26.4l-76 57.6c-20.8 16-26.4 44-12.8 68l84.8 143.2c9.6 16.8 28 27.2 47.2 27.2 6.4 0 12-0.8 18.4-3.2L312 796c15.2 10.4 31.2 19.2 47.2 26.4l13.6 92c3.2 25.6 26.4 45.6 53.6 45.6h171.2c27.2 0 49.6-19.2 53.6-44.8l13.6-92.8c16-7.2 31.2-16 47.2-26.4l90.4 35.2c6.4 2.4 12.8 3.2 19.2 3.2 19.2 0 37.6-9.6 46.4-26.4l85.6-144.8c12.8-23.2 7.2-51.2-13.6-67.2l-76-57.6c0.8-8 1.6-16.8 1.6-26.4 0-9.6-0.8-18.4-1.6-26.4l76-57.6c20.8-16 26.4-44 12.8-68l-84.8-143.2c-9.6-16.8-28-27.2-47.2-27.2-6.4 0-12 0.8-18.4 3.2L712 228c-15.2-10.4-31.2-19.2-47.2-26.4l-13.6-92c-4-26.4-26.4-45.6-53.6-45.6zM512 384c70.4 0 128 57.6 128 128s-57.6 128-128 128-128-57.6-128-128 57.6-128 128-128m0-64c-105.6 0-192 86.4-192 192s86.4 192 192 192 192-86.4 192-192-86.4-192-192-192z" p-id="2921" /></symbol>'});s().add(l);t["default"]=l},7790:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_msg",use:"icon-icon_msg-usage",viewBox:"0 0 16 16",content:'<symbol viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_msg">\n    <title>切片</title>\n    <defs>\n        <polygon id="icon-icon_msg_path-1" points="0 0 16 0 16 16 0 16" />\n    </defs>\n    <g id="icon-icon_msg_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_msg_画板" transform="translate(-1780.000000, -132.000000)">\n            <g id="icon-icon_msg_导航8备份-2" transform="translate(-1.000000, 100.000000)">\n                <g id="icon-icon_msg_编组-18" transform="translate(1693.000000, 24.000000)">\n                    <g id="icon-icon_msg_Icon/fa-comments" transform="translate(88.000000, 8.000000)">\n                        <mask id="icon-icon_msg_mask-2" fill="white">\n                            <use xlink:href="#icon-icon_msg_path-1" />\n                        </mask>\n                        <g id="icon-icon_msg_Clip-2"></g>\n                        <path d="M15,2 L15,12 L8,12 L4,14 L4,12 L1,12 L1,2 L15,2 Z M14,3 L2,3 L2,11 L5,11 L5,12.381 L7.76393202,11 L14,11 L14,3 Z" id="icon-icon_msg_形状结合" fill-opacity="0.6" fill="currentColor" fill-rule="nonzero" mask="url(#icon-icon_msg_mask-2)" />\n                        <rect id="icon-icon_msg_矩形" fill-opacity="0.6" fill="currentColor" mask="url(#icon-icon_msg_mask-2)" x="4" y="5" width="7" height="1" />\n                        <rect id="icon-icon_msg_矩形备份" fill-opacity="0.6" fill="currentColor" mask="url(#icon-icon_msg_mask-2)" x="4" y="8" width="5" height="1" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},8018:function(e,t,n){"use strict";n.d(t,{A:function(){return f}});n(71201),n(66040),n(48773);var i=n(50443),o=n(10514),a=n(50896),s=n(82157),l=n(49928);const r={class:"smgt-bread"},c={class:"first-level"},u={class:"second-level"},d={key:0,class:"item-content"};var m=(0,i.pM)({__name:"index",setup(e){const{t:t}=(0,l.s9)(),n=(0,s.lq)();console.log(n),console.log(a.r),console.log(n.fullPath);const m=(0,i.EW)(()=>{let e=t(`routes.${n.name}`),i=[];return n.matched.forEach(e=>{i.push({name:t(`routes.${e.name}`),icon:e.meta.icon})}),{name:e,breadTree:i}});return(e,t)=>{const n=(0,i.g2)("svg-icon");return(0,i.uX)(),(0,i.CE)("div",r,[(0,i.Lk)("div",c,(0,o.v_)(m.value.name),1),(0,i.Lk)("div",u,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(m.value.breadTree,(e,t)=>((0,i.uX)(),(0,i.CE)("div",{key:e,class:"bread-item"},[0!==t?((0,i.uX)(),(0,i.CE)("div",d,[1==t?((0,i.uX)(),(0,i.Wv)(n,{key:0,name:e.icon,class:"menu-icon"},null,8,["name"])):(0,i.Q3)("",!0),(0,i.Lk)("span",null,(0,o.v_)(e.name)+(0,o.v_)(t+1===m.value.breadTree.length?"":" 〉"),1)])):(0,i.Q3)("",!0)]))),128))])])}}}),g=n(48057);const p=(0,g.A)(m,[["__scopeId","data-v-7556b7ac"]]);var f=p},8540:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_jglb",use:"icon-icon_jglb-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_jglb">\n    <title>icon_仿真结果列表</title>\n    <g id="icon-icon_jglb_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_jglb_仿真与故障恢复建议_仿真模板管理" transform="translate(-60.000000, -272.000000)">\n            <g id="icon-icon_jglb_告警规则备份-5" transform="translate(16.000000, 260.000000)">\n                <g id="icon-icon_jglb_icon_仿真结果列表" transform="translate(44.000000, 12.000000)">\n                    <rect id="icon-icon_jglb_矩形备份-55" x="0" y="0" width="20" height="20" />\n                    <path d="M14,2 C14.5522847,2 15,2.44771525 15,3 L15,8 L14,8 L14,3 L4,3 L4,16 L9,16 L9.73221648,17 L4,17 C3.44771525,17 3,16.5522847 3,16 L3,3 C3,2.44771525 3.44771525,2 4,2 L14,2 Z M8,11 L8,12 L5,12 L5,11 L8,11 Z M9,8 L9,9 L5,9 L5,8 L9,8 Z M12,5 L12,6 L5,6 L5,5 L12,5 Z" id="icon-icon_jglb_形状结合" fill-opacity="0.6" fill="currentColor" fill-rule="nonzero" />\n                    <path d="M12.1468031,9.03868757 C12.1849125,9.06047935 12.2169342,9.09044236 12.2402233,9.12610163 L14.9173893,13.2252701 C15.0275369,13.3939237 15.0275369,13.6060763 14.9173893,13.7747299 L12.2402233,17.8738984 C12.1591462,17.9980404 11.9858685,18.037177 11.8531969,17.9613124 C11.8150875,17.9395207 11.7830658,17.9095576 11.7597767,17.8738984 L9.08261074,13.7747299 C8.97246309,13.6060763 8.97246309,13.3939237 9.08261074,13.2252701 L11.7597767,9.12610163 C11.8408538,9.00195961 12.0141315,8.962823 12.1468031,9.03868757 Z M12,10.587 L10.097,13.499 L12,16.412 L13.902,13.499 L12,10.587 Z" id="icon-icon_jglb_形状结合" fill-opacity="0.6" fill="currentColor" fill-rule="nonzero" />\n                    <path d="M14.8199303,10.0235344 L17.3172328,13.2654977 L15.1800697,16.9764656 L12.6827672,13.7345023 L14.8199303,10.0235344 Z" id="icon-icon_jglb_矩形备份-4" stroke-opacity="0.6" stroke="currentColor" fill-rule="nonzero" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},10640:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_user2",use:"icon-icon_user2-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_user2">\n    <title>切片</title>\n    <g id="icon-icon_user2_页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_user2_登录页" transform="translate(-1374.000000, -427.000000)">\n            <g id="icon-icon_user2_Icon/User" transform="translate(1374.000000, 427.000000)">\n                <rect id="icon-icon_user2_矩形" fill="currentColor" fill-rule="nonzero" opacity="0" x="0" y="0" width="20" height="20" />\n                <path d="M16.875,18.75 C16.875,14.6078644 13.7969577,11.25 10,11.25 C6.20304235,11.25 3.125,14.6078644 3.125,18.75" id="icon-icon_user2_椭圆形" stroke="currentColor" />\n                <circle id="icon-icon_user2_椭圆形" stroke="currentColor" cx="10" cy="6.25" r="4.5" />\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},15461:function(e,t,n){"use strict";n.d(t,{T7:function(){return l},rQ:function(){return a},zM:function(){return s}});var i=n(56771),o=n(40641);const a=()=>i.A.get("/overview/selectWarningTotal"),s=e=>i.A.get("/overview/selectWarningList",{params:e}),l=()=>o.A.get("/virtual/base/data/heartbeat")},16744:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_password",use:"icon-icon_password-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_password">\n    <title>切片</title>\n    <g id="icon-icon_password_页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_password_登录页" transform="translate(-1374.000000, -553.000000)" fill="currentColor">\n            <g id="icon-icon_password_Icon/password" transform="translate(1374.000000, 553.000000)">\n                <path d="M13.125,1.24992304 C10.0183983,1.24992304 7.5,3.76839828 7.5,6.875 C7.49945547,7.42786232 7.58161455,7.97769618 7.74375,8.50625 L1.25,15 L1.25,18.75 L5,18.75 L11.49375,12.25625 C13.1224059,12.7559694 14.8897845,12.4906599 16.2999324,11.5347716 C17.7100802,10.5788832 18.6110442,9.03541954 18.75,7.3375 C18.876182,6.00346831 18.5093201,4.66942486 17.71875,3.5875 C16.6555715,2.11134692 14.9441443,1.24049827 13.125,1.24992304 Z M13.125,11.25 C12.7014539,11.2483365 12.2803939,11.1851775 11.875,11.0625 L11.15625,10.84375 L10.625,11.375 L8.6375,13.3625 L7.75625,12.5 L6.875,13.38125 L7.7375,14.24375 L6.74375,15.2375 L5.88125,14.375 L5,15.25625 L5.8625,16.11875 L4.48125,17.5 L2.5,17.5 L2.5,15.51875 L8.625,9.375 L9.15625,8.84375 L8.975,8.25 C8.32612935,6.20956901 9.21791173,3.99585038 11.1,2.975 C12.4361377,2.27651536 14.0377764,2.3152207 15.3386148,3.07743072 C16.6394533,3.83964075 17.4561724,5.21794197 17.5,6.725 C17.5433722,7.60944727 17.3048308,8.48482867 16.81875,9.225 C16.0148858,10.4873327 14.621556,11.2511886 13.125,11.25 Z M13.75,7.5 C14.4403559,7.5 15,6.94035594 15,6.25 C15,5.55964406 14.4403559,5 13.75,5 C13.0596441,5 12.5,5.55964406 12.5,6.25 C12.5,6.94035594 13.0596441,7.5 13.75,7.5 Z" id="icon-icon_password_Fill" />\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},16880:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_info-filled",use:"icon-icon_info-filled-usage",viewBox:"0 0 24 24",content:'<symbol viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_info-filled">\n    <title>切片</title>\n    <g id="icon-icon_info-filled_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_info-filled_仿真与故障恢复建议_角色管理_重启角色" transform="translate(-784.000000, -468.000000)" fill="currentColor">\n            <g id="icon-icon_info-filled_编组" transform="translate(760.000000, 444.000000)">\n                <g id="icon-icon_info-filled_Icon/info-filled" transform="translate(24.000000, 24.000000)">\n                    <path d="M12,1.5 C17.7989899,1.5 22.5,6.20101013 22.5,12 C22.5,17.7989899 17.7989899,22.5 12,22.5 C6.20101013,22.5 1.5,17.7989899 1.5,12 C1.5,6.20101013 6.20101013,1.5 12,1.5 Z M12.9375,10.5 L11.0625,10.5 L11.0625,17.25 L12.9375,17.25 L12.9375,10.5 Z M12,6.375 C11.3786797,6.375 10.875,6.87867966 10.875,7.5 C10.875,8.12132034 11.3786797,8.625 12,8.625 C12.6213203,8.625 13.125,8.12132034 13.125,7.5 C13.125,6.87867966 12.6213203,6.375 12,6.375 Z" id="icon-icon_info-filled_形状结合" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},17771:function(e,t,n){"use strict";n.d(t,{jd:function(){return o},nD:function(){return a},pG:function(){return s}});var i=n(56771);const o=e=>i.A.get("/sys/logout",{params:e}),a=e=>i.A.get("/sys/user/selectUserMenuList",{params:e}),s=e=>i.A.get("/sys/personal/select",{params:e})},19241:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-lf_scale_plus",use:"icon-lf_scale_plus-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-lf_scale_plus">\n    <title>切片</title>\n    <g id="icon-lf_scale_plus_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-lf_scale_plus_仿真与故障恢复建议_工况列表_查看详情备份" transform="translate(-1842.000000, -1007.000000)">\n            <g id="icon-lf_scale_plus_编组" transform="translate(1592.000000, 998.000000)">\n                <g id="icon-lf_scale_plus_Icon/add" transform="translate(250.000000, 9.000000)">\n                    <rect id="icon-lf_scale_plus_Rectangle" x="0.875" y="0.875" width="12.25" height="12.25" />\n                    <path d="M7.4375,7.4375 L7.4375,11.7565274 C7.4375,12.0293081 7.26996124,12.25 7,12.25 C6.73003876,12.25 6.5625,12.0293081 6.5625,11.7565274 L6.5625,7.4375 L2.23837209,7.4375 C1.96841085,7.4375 1.75,7.27278068 1.75,7 C1.75,6.72721932 1.96841085,6.5625 2.23837209,6.5625 L6.5625,6.5625 L6.5625,2.24347258 C6.5625,1.97069191 6.73003876,1.75 7,1.75 C7.26996124,1.75 7.4375,1.97069191 7.4375,2.24347258 L7.4375,6.5625 L11.7616279,6.5625 C12.0315891,6.5625 12.25,6.72721932 12.25,7 C12.25,7.27278068 12.0315891,7.4375 11.7616279,7.4375 L7.4375,7.4375 Z" id="icon-lf_scale_plus_Shape" fill="#525866" transform="translate(7.000000, 7.000000) scale(1, -1) translate(-7.000000, -7.000000) " />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},19425:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_dsrw",use:"icon-icon_dsrw-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_dsrw">\n    <title>icon_定时仿真任务列表</title>\n    <g id="icon-icon_dsrw_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_dsrw_仿真与故障恢复建议_仿真模板管理" transform="translate(-60.000000, -220.000000)">\n            <g id="icon-icon_dsrw_icon_定时仿真任务列表" transform="translate(60.000000, 220.000000)">\n                <rect id="icon-icon_dsrw_矩形备份-55" x="0" y="0" width="20" height="20" />\n                <path d="M14,9.5 C16.209139,9.5 18,11.290861 18,13.5 C18,15.709139 16.209139,17.5 14,17.5 C11.790861,17.5 10,15.709139 10,13.5 C10,11.290861 11.790861,9.5 14,9.5 Z M14,2 C14.5522847,2 15,2.44771525 15,3 L15.0011864,8.60025909 C14.6777087,8.5345146 14.342885,8.5 14,8.5 L14,3 L4,3 L4,16 L9.66966156,16.0012971 C9.88116163,16.3666647 10.1375754,16.7027855 10.4313517,17.0021084 L4,17 C3.44771525,17 3,16.5522847 3,16 L3,3 C3,2.44771525 3.44771525,2 4,2 L14,2 Z M14,10.5 C12.3431458,10.5 11,11.8431458 11,13.5 C11,15.1568542 12.3431458,16.5 14,16.5 C15.6568542,16.5 17,15.1568542 17,13.5 C17,11.8431458 15.6568542,10.5 14,10.5 Z M14.5,11.5 L14.5,13 L16,13 L16,14 L13.5,14 L13.5,11.5 L14.5,11.5 Z M8,11 L8,12 L5,12 L5,11 L8,11 Z M9,8 L9,9 L5,9 L5,8 L9,8 Z M12,5 L12,6 L5,6 L5,5 L12,5 Z" id="icon-icon_dsrw_形状结合" fill-opacity="0.6" fill="currentColor" fill-rule="nonzero" />\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},27103:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_dqyz",use:"icon-icon_dqyz-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_dqyz">\n    <title>icon_仿真电气阈值</title>\n    <g id="icon-icon_dqyz_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_dqyz_仿真与故障恢复建议_仿真模板管理" transform="translate(-62.000000, -325.000000)">\n            <g id="icon-icon_dqyz_告警规则" transform="translate(16.000000, 312.000000)">\n                <g id="icon-icon_dqyz_icon_仿真电气阈值" transform="translate(46.000000, 13.000000)">\n                    <rect id="icon-icon_dqyz_矩形备份-55" x="0" y="0" width="20" height="20" />\n                    <path d="M10,2 C14.418278,2 18,5.581722 18,10 C18,14.418278 14.418278,18 10,18 C5.581722,18 2,14.418278 2,10 C2,7.790861 2.8954305,5.790861 4.34314575,4.34314575 L5.05025253,5.05025253 C3.78350169,6.31700338 3,8.06700338 3,10 C3,13.8659932 6.13400675,17 10,17 C13.8659932,17 17,13.8659932 17,10 C17,6.4738781 14.3928118,3.55669765 11.0010101,3.07103341 L11,6 L10,6 L10,2 Z M6.75,4.87083488 L10.4985762,9.64640197 C10.7943307,10.0231838 10.728645,10.5683822 10.3518632,10.8641367 C10.3195811,10.8894765 10.2855412,10.912493 10.25,10.9330127 C9.83517901,11.1725097 9.30474946,11.0303815 9.06525245,10.6155606 C9.04473273,10.5800194 9.02676154,10.5430665 9.01147434,10.5049806 L6.75,4.87083488 Z" id="icon-icon_dqyz_形状结合" fill-opacity="0.6" fill="currentColor" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},33263:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_mbgl",use:"icon-icon_mbgl-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_mbgl">\n    <title>icon_仿真模板管理</title>\n    <g id="icon-icon_mbgl_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_mbgl_仿真与故障恢复建议_仿真模板管理" transform="translate(-62.000000, -377.000000)">\n            <g id="icon-icon_mbgl_告警规则备份-3" transform="translate(16.000000, 364.000000)">\n                <g id="icon-icon_mbgl_icon_仿真模板管理" transform="translate(46.000000, 13.000000)">\n                    <rect id="icon-icon_mbgl_矩形备份-55" x="0" y="0" width="20" height="20" />\n                    <path d="M17,12 L19,15.5 L17,19 L13,19 L11,15.5 L13,12 L17,12 Z M15,2 C15.5522847,2 16,2.44771525 16,3 L16,4 L17,4 C17.5522847,4 18,4.44771525 18,5 L18,11 L17,11 L17,5 L5,5 L5,17 L10,17 L10,18 L5,18 C4.44771525,18 4,17.5522847 4,17 L4,16 L3,16 C2.44771525,16 2,15.5522847 2,15 L2,3 C2,2.44771525 2.44771525,2 3,2 L15,2 Z M16.419,13 L13.58,13 L12.152,15.499 L13.581,18 L16.418,18 L17.847,15.499 L16.419,13 Z M15,14.5 C15.5522847,14.5 16,14.9477153 16,15.5 C16,16.0522847 15.5522847,16.5 15,16.5 C14.4477153,16.5 14,16.0522847 14,15.5 C14,14.9477153 14.4477153,14.5 15,14.5 Z M15,3 L3,3 L3,15 L4,15 L4,5 C4,4.44771525 4.44771525,4 5,4 L15,4 L15,3 Z M14,8 L14,9 L8,9 L8,8 L14,8 Z" id="icon-icon_mbgl_形状结合" fill="currentColor" fill-rule="nonzero" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},33337:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-export",use:"icon-export-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-export">\n    <title>切片</title>\n    <g id="icon-export_页面" stroke-width="1" fill-rule="evenodd">\n        <g id="icon-export_仿真与故障恢复建议_工况列表_配置参数仿真（InfeedBreaker）" transform="translate(-1758.000000, -620.000000)" fill-rule="nonzero">\n            <g id="icon-export_编组-12" transform="translate(288.000000, 548.000000)">\n                <g id="icon-export_button/primary_icon备份-3" transform="translate(1458.000000, 63.000000)">\n                    <g id="icon-export_Icon/export" transform="translate(12.000000, 9.000000)">\n                        <polygon id="icon-export_路径" points="10.7866171 8.58448473 13.125 10.8928884 10.7866171 13.201292 10.1719032 12.5785948 11.391625 11.3748597 7 11.375 7 10.5 11.48175 10.4998597 10.1719032 9.20718194" />\n                        <path d="M11.375,7.875 L10.5,7.875 L10.5,5.25 L7,5.25 L7,1.75 L1.75,1.75 L1.75,12.25 L7,12.25 L7,13.125 L0.875,13.125 L0.875,0.875 L7.875,0.875 L11.375,4.375 L11.375,7.875 Z M7.875,2.113125 L7.875,4.375 L10.136875,4.375 L7.875,2.113125 Z" id="icon-export_形状结合" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},33686:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_delete",use:"icon-icon_delete-usage",viewBox:"0 0 16 16",content:'<symbol viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_delete">\n    <title>切片</title>\n    <g id="icon-icon_delete_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_delete_仿真与故障恢复建议_用户管理_批量添加" transform="translate(-869.000000, -571.000000)" fill="currentColor">\n            <g id="icon-icon_delete_编组-40" transform="translate(-10.000000, 0.000000)">\n                <g id="icon-icon_delete_弹窗" transform="translate(690.000000, 402.000000)">\n                    <g id="icon-icon_delete_Icon/delete" transform="translate(189.000000, 169.000000)">\n                        <path d="M6,6 L7,6 L7,12 L6,12 L6,6 Z M9,6 L10,6 L10,12 L9,12 L9,6 Z M2,3 L2,4 L3,4 L3,14 C3,14.6 3.4,15 4,15 L12,15 C12.6,15 13,14.6 13,14 L13,4 L14,4 L14,3 L2,3 Z M4,14 L4,4 L12,4 L12,14 L4,14 Z M6,1 L10,1 L10,2 L6,2 L6,1 Z" id="icon-icon_delete_Fill" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},36322:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-lf_select_arrow",use:"icon-lf_select_arrow-usage",viewBox:"0 0 12 12",content:'<symbol viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-lf_select_arrow">\n    <title>切片</title>\n    <g id="icon-lf_select_arrow_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-lf_select_arrow_仿真与故障恢复建议_工况列表_查看详情备份" transform="translate(-1672.000000, -1008.000000)">\n            <g id="icon-lf_select_arrow_编组" transform="translate(1592.000000, 998.000000)">\n                <g id="icon-lf_select_arrow_Icon/down" transform="translate(80.000000, 10.000000)">\n                    <rect id="icon-lf_select_arrow_矩形" x="0" y="0" width="12" height="12" />\n                    <polygon id="icon-lf_select_arrow_三角形" fill="#A5A8AD" transform="translate(6.000000, 6.000000) scale(1, -1) translate(-6.000000, -6.000000) " points="6 3 11 9 1 9" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},36334:function(e,t,n){"use strict";n.d(t,{A:function(){return W}});n(71201),n(66040),n(48773);var i=n(50443),o=n(10514),a=n(55787),s=n(38006),l=n.p+"static/img/login_logo_en.c03d0c79.png",r="data:image/png;base64,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",c="data:image/png;base64,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",u=n(82157),d=n(68265),m=n(60747),g=n(3255),p=n(49928),f=n(15461),h=n(34686),w=n(6666),v=n.n(w);const _={class:"navbar-main"},y={class:"navbar-content"},C={class:"logo-nav-main"},b={class:"logo-main"},L={key:0,src:l,alt:"",class:"logo-img"},T={key:1,src:r,alt:"",class:"logo-img"},k={class:"title-box"},x={key:0,class:"page-sub-title"},S=["onClick"],D={src:c,alt:"",class:"nav-bg"},A={class:"info-main"},M={class:"el-dropdown-link"},P={class:"message-popover-box"},E={class:"message-popover-main"},R={class:"message-popover-nav"},I={class:"message-content-main"},j={class:"title-p"},F={class:"content-div"},z={class:"content-p"},B={key:0,class:"tag-div pending-tag"},N={key:1,class:"tag-div processed-tag"},V={class:"time-div"},O={class:"time-p"},U={class:"account-box"},H={class:"account-div"};var Z=(0,i.pM)({__name:"Navbar",setup(e){const{t:t}=(0,p.s9)(),n=(0,u.lq)(),l=((0,u.rd)(),(0,d.A)()),r=(0,m.A)(),c=(0,g.A)(),w=(0,a.Kh)({id:null,email:null,userName:null}),Z=(0,i.EW)(()=>l.lang),G=(0,i.EW)(()=>ue(l.lang)),Q=(0,i.EW)(()=>{let e="zh_CN"==l.lang?"cn":"zh_HK"==l.lang?"tc":"en";return e+"Name"}),W=(0,i.EW)(()=>c.allRoutes.list||[]),X=(0,a.KR)(0),K=(0,a.KR)(1),q=(0,a.KR)([]),J=(0,a.KR)([]),Y=(0,a.KR)([]),$=(0,a.KR)(!1),ee=(0,a.KR)(!1);let te=location.host,ne=new WebSocket("https:"==window.location.protocol?"wss://"+te+"/mtr/ws/websocket":"ws://"+te+"/mtr/ws/websocket");function ie(){const{meta:e}=n;let t=W.value,i=e.menu;t.forEach(e=>{e?.path&&e.path.includes(i)&&(e?.childList?.length>0?c.setRoutes(e.childList):c.setRoutes([]))})}(0,i.sV)(()=>{w.id=r.userInfo?.id,w.email=r.userInfo?.email,w.userName=r.userInfo?.userName,ie(),le()}),ne.onopen=function(){console.log("WebSocket连接已建立")},ne.onmessage=function(e){console.log("Message Data:"+e.data);let t=JSON.parse(e.data||"{}");1==t.type?X.value++:2==t.type&&(X.value--,X.value<0&&(X.value=0)),ee.value&&re()},ne.onclose=function(){console.log("WebSocket连接已断开")};const oe=()=>{l.findMenuCode("M50303")?window.open("/equ/fault/record","_blank"):(0,h.nk)({message:t("page.overview.promptText1"),type:"warning",offset:80,duration:3e3})},ae=()=>{ee.value=!0,re()},se=()=>{ee.value=!1},le=()=>{(0,f.rQ)().then(e=>{200==e.code&&(X.value=e.data||0)})},re=()=>{$.value=!0;let e={limit:1e4,page:1};Promise.all([(0,f.zM)({...e,alarmLevel:1}),(0,f.zM)({...e,alarmLevel:2})]).then(e=>{console.log(e),q.value=[],J.value=[],e.forEach(e=>{200==e.code&&e.data.list.forEach(e=>{1==e.alarmLevel&&q.value.push(e),2==e.alarmLevel&&J.value.push(e)})})}).finally(()=>{ce(K.value),$.value=!1})},ce=e=>{K.value=e,1==e&&(Y.value=JSON.parse(JSON.stringify(q.value))),2==e&&(Y.value=JSON.parse(JSON.stringify(J.value)))};function ue(e){return t("zh_CN"==e?"golbal.cn":"zh_HK"==e?"golbal.tc":"golbal.en")}function de(e){l.setLang(e)}function me(e){const{meta:t}=n;return!(!t.code||!t.code?.includes(e))}function ge(e){let t=e.path;window.location.href=t,"M4"==e.code&&c.setRoutes(e.childList)}const pe=()=>{r.userLogout()};return(e,n)=>{const l=(0,i.g2)("CaretBottom"),r=(0,i.g2)("el-icon"),c=(0,i.g2)("el-dropdown-item"),u=(0,i.g2)("el-dropdown-menu"),d=(0,i.g2)("el-dropdown"),m=(0,i.g2)("svg-icon"),g=(0,i.g2)("el-badge"),p=(0,i.g2)("ArrowRight"),f=(0,i.g2)("el-scrollbar"),h=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",_,[n[4]||(n[4]=(0,i.Lk)("div",{class:"navbar-bg"},null,-1)),(0,i.Lk)("div",y,[(0,i.Lk)("div",C,[(0,i.Lk)("div",b,["en_US"===Z.value?((0,i.uX)(),(0,i.CE)("img",L)):((0,i.uX)(),(0,i.CE)("img",T)),(0,i.Lk)("div",k,[(0,i.Lk)("p",{class:(0,o.C4)(["page-title",{"en-title":"en_US"==Z.value}])},(0,o.v_)((0,a.R1)(t)("golbal.projectName")),3),"zh_CN"==Z.value||"zh_HK"==Z.value?((0,i.uX)(),(0,i.CE)("span",x," Digital Twins System (Power supply) ")):(0,i.Q3)("",!0)])]),(0,i.Lk)("div",{class:(0,o.C4)(["nav-main",{"en-nav-main":"en_US"==Z.value}])},[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(W.value,e=>((0,i.uX)(),(0,i.CE)("div",{key:e.id,class:(0,o.C4)(["nav-item",{"nav-active":me(e.code)}]),onClick:t=>ge(e)},[(0,i.eW)((0,o.v_)(e[Q.value])+" ",1),(0,i.bo)((0,i.Lk)("img",D,null,512),[[s.aG,me(e.code)]])],10,S))),128))],2)]),(0,i.Lk)("div",A,[(0,i.bF)(d,{class:"language-dropdown"},{dropdown:(0,i.k6)(()=>[(0,i.bF)(u,null,{default:(0,i.k6)(()=>[(0,i.bF)(c,{onClick:n[0]||(n[0]=e=>de("zh_HK"))},{default:(0,i.k6)(()=>[(0,i.eW)((0,o.v_)((0,a.R1)(t)("golbal.tc")),1)]),_:1}),(0,i.bF)(c,{onClick:n[1]||(n[1]=e=>de("en_US"))},{default:(0,i.k6)(()=>[(0,i.eW)((0,o.v_)((0,a.R1)(t)("golbal.en")),1)]),_:1})]),_:1})]),default:(0,i.k6)(()=>[(0,i.Lk)("span",M,[(0,i.eW)((0,o.v_)(G.value)+" ",1),(0,i.bF)(r,{class:"el-icon--right"},{default:(0,i.k6)(()=>[(0,i.bF)(l)]),_:1})])]),_:1}),(0,i.Lk)("div",{class:"msg-icon",onMouseenter:ae,onMouseleave:se},[(0,i.bF)(g,{value:X.value,hidden:!(X.value>0),class:"msg-badge"},{default:(0,i.k6)(()=>[(0,i.bF)(m,{name:"icon_msg"})]),_:1},8,["value","hidden"]),(0,i.Lk)("div",P,[(0,i.Lk)("div",E,[(0,i.Lk)("div",R,[(0,i.Lk)("div",{class:(0,o.C4)("message-nav-item "+(1==K.value?"active-item":"")),onClick:n[2]||(n[2]=e=>ce(1))},(0,o.v_)((0,a.R1)(t)("page.topNavigation.accident"))+"("+(0,o.v_)(q.value.length)+") ",3),(0,i.Lk)("div",{class:(0,o.C4)("message-nav-item "+(2==K.value?"active-item":"")),onClick:n[3]||(n[3]=e=>ce(2))},(0,o.v_)((0,a.R1)(t)("page.topNavigation.abnormal"))+"("+(0,o.v_)(J.value.length)+") ",3)]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(f,{height:"358px"},{default:(0,i.k6)(()=>[(0,i.Lk)("div",I,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(Y.value,(e,n)=>((0,i.uX)(),(0,i.CE)("div",{key:n,class:"message-content-item"},[(0,i.Lk)("p",j,(0,o.v_)(e.equipmentName),1),(0,i.Lk)("div",F,[(0,i.Lk)("p",z,(0,o.v_)(e.faultDescription),1),2==e.faultStatus?((0,i.uX)(),(0,i.CE)("div",B,(0,o.v_)((0,a.R1)(t)("page.topNavigation.pendingStatus")),1)):(0,i.Q3)("",!0),1==e.faultStatus?((0,i.uX)(),(0,i.CE)("div",N,(0,o.v_)((0,a.R1)(t)("page.topNavigation.processedStatus")),1)):(0,i.Q3)("",!0)]),(0,i.Lk)("div",V,[(0,i.Lk)("p",O,(0,o.v_)((0,a.R1)(v())(e.faultDate).format("DD/MM/YYYY HH:mm:ss")),1),(0,i.Lk)("a",{class:"detail-a",onClick:oe},[(0,i.eW)((0,o.v_)((0,a.R1)(t)("page.topNavigation.viewDetails"))+" ",1),(0,i.bF)(r,null,{default:(0,i.k6)(()=>[(0,i.bF)(p)]),_:1})])])]))),128))])]),_:1})),[[h,$.value]])])])],32),(0,i.bF)(d,{class:"language-dropdown"},{dropdown:(0,i.k6)(()=>[(0,i.bF)(u,null,{default:(0,i.k6)(()=>[(0,i.bF)(c,{onClick:pe},{default:(0,i.k6)(()=>[(0,i.eW)((0,o.v_)((0,a.R1)(t)("golbal.logout")),1)]),_:1})]),_:1})]),default:(0,i.k6)(()=>[(0,i.Lk)("div",U,[(0,i.Lk)("div",H,[(0,i.bF)(m,{name:"icon_user3"})]),(0,i.Lk)("span",null,(0,o.v_)(w.userName),1),(0,i.bF)(r,{class:"el-icon--right"},{default:(0,i.k6)(()=>[(0,i.bF)(l)]),_:1})])]),_:1})])])])}}}),G=n(48057);const Q=(0,G.A)(Z,[["__scopeId","data-v-3f607412"]]);var W=Q},37992:function(e,t,n){"use strict";n.d(t,{A:function(){return c}});var i=n(49928),o={golbal:{projectName:"智慧供电孪生系统",cn:"简体",tc:"繁体",en:"英文",paginationLabel1:"第",paginationLabel2:"条",paginationLabel3:"总共",paginationLabel4:"条/页",status1:"停用",status2:"启用",status3:"不限",status4:"启用",btnText1:"取消",btnText2:"确认",btnText3:"继续",btnText4:"保存",dialogPrompt1:"确认要继续么?",placeholder1:"请输入",logout:"退出登录",mustEnter:" 表示必填",dialogPrompt2:"最大字符限制为{size}个字符",requestTimeout:"网络连接超时,请检查您的网络连接。如果问题仍然存在,请联系技术支持。"},routes:{404:"404",simulationMGT:"仿真与故障恢复建议",SIM:"仿真与故障恢复建议",operatingCondition:"工况列表",scheduledTask:"定时仿真任务列表",taskDetail:"仿真任务配置",result:"仿真结果列表",electricalThreshold:"仿真电气阈值",templateMGT:"仿真模板管理",viewDetail:"查看詳情",editDetail:"编辑詳情",operatingConditionViewDetail:"查看详情",operatingConditionEditDetail:"编辑详情",resultViewDetail:"查看详情",templateMGTViewDetail:"查看详情",templateMGTEditDetail:"编辑详情",overview:"综合概览",overviewEnergy:"综合概览",mount:"仿真挂载设置",SIMSetting:"仿真设置"},page:{error404:{title:"出错啦",subTitle:"亲爱的用户,您访问的页面不存在..."},electricalThreshold:{input_placeholder:"请输入站点, 支持模糊搜索",import_file:"导入文件",export_file:"导出文件",titleAC:"交流AC阈值",titleDC:"直流DC阈值",tip:"不填视为无阈值 ",btnText1:"取消",btnText2:"保存修改",vbus:"母线电压",re:"再生能量捕获装置",rt:"整流机组{no}",st:"配电变压器{no}",irt:"整流机组馈线{no}电流",ist:"配电变馈线{no}电流 ",ire:"再生设备馈线电流",icable:"电缆电流",load:"有功功率",iout:"输出电流",tss:"牵引变电所",utLeft:"上行轨道左侧直流馈线断路器的打开/关闭状态（1关闭，0打开）",dtLeft:"下行轨道左侧直流馈线断路器的打开/关闭状态（1关闭，0打开）",utRight:"上行轨道右侧直流馈线断路器的打开/关闭状态（1关闭，0打开）",dtRight:"下行轨道右侧直流馈线断路器的打开/关闭状态（1关闭，0打开）",nfeeder:"整站的电压",rtPower15s:"整流变压器15s功率的均方根值",rtPower60s:"整流变压器60s功率的均方根值",rtPowerLt:"整流变压器长期功率的均方根值",rePower10s:"再生能量捕获装置10s功率的均方根值",rePower60s:"再生能量捕获装置60s功率的均方根值",rePowerLt:"再生能量捕获装置长期功率的均方根值",vnet:"截面中的最低大修电压",rail:"轨道电位",ovpd:"通过OVPD的电流",utbypass:"上行轨道旁路隔离器开闭状态",dtbypass:"下行轨道旁路隔离器开闭状态",powerP:"有功功率",powerQ:"无功功率",rtPowerP:"整流机组{no}有功功率",rtPowerQ:"整流机组{no}无功功率",stPowerP:"配电变压器{no}有功功率",stPowerQ:"配电变压器{no}无功功率",rePowerP:"再生能量捕获装置有功功率",rePowerQ:"再生能量捕获装置无功功率",number:"整流机组数量",RT_EFF:"整流机组效率",RE_EFF:"再生能量捕获装置效率",ST_EFF:"配电变压器效率",tip1:"请检查, 最小值不得超过最大值！",tip2:"值不能为空！"},operatingCondition:{type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工况名称",namePlaceholder:"请输入工况名称",triggerMode:"触发方式",simulationStatus:"仿真状态",createBy:"创建人",createByPlaceholder:"请输入创建人",addCondition:"新增工况",runSimulation:"执行仿真",copyCondition:"复制工况",deleteCondition:"删除工况",conditionId:"工况ID",faultId:"故障ID",column_name:"工况名称",column_type:"类型",creator:"创建人",creationTime:"创建时间",lastModifiedBy:"最后修改人",lastModifiedTime:"最后修改时间",historicalCount:"历史仿真次数",simJobTotal:"当前任务数",operation:"操作",viewDetails:"查看详情",configScheduleTask:"配置定时任务",viewTask:"查看任务",selected:"已选",item:"项",query:"查询",pleaseEnter:"请输入工况名称",pleaseFaultId:"请输入故障ID",addDialogTitle:"新增工况",remark:"描述",pleaseEnterRemark:"请输入工况描述",templateType:"工况类型",pleaseSelectType:"请选择工况类型",template:"工况模板",pleaseSelectTemplate:"请选择工况模板",addSuccess:"添加成功",addFail:"添加失败",quit:"取 消",confirm:"确 认",batchDisableChoosePrompt:"请选择工况后再执行该操作",deleteTitle:"永久删除工况",deleteContent1:"工况删除后不可恢复,",deleteContent2:"确认要继续么?",deletePromptText:"该工况已永久删除",batchDisableTitle:"批量删除工况",batchDisableContent1:"选中工况删除后不可恢复,",batchDisablePromptText:"选中工况已删除",edit:"编辑",confirmAgain:"再次确认，",isContinue:"是否对当前选择的工况执行仿真?",simulationExecuted:"工况仿真已经执行",executionException:"执行异常",originalCondition:"原工况",newConditionName:"新工况名称"},templateMGT:{type:"交流/直流",noLimit:"不限",type1:"交流/AC",type2:"直流/DC",name:"模板名称",namePlaceholder:"请输入模板名称",createBy:"创建人",createByPlaceholder:"请输入创建人",addTemplate:"添加模板",deleteTemplate:"删除模板",noPlaceholder:"请选择案例编号",scenarioTypePlaceholder:"请选择场景类型",operationScenarioPlaceholder:"请输入操作场景",column_name:"模板名称",column_type:"类型",column_no:"用例编号",column_scenarioType:"场景类型",column_operationScenario:"操作场景",column_state:"状态",creator:"创建人",creationTime:"创建时间",lastModifiedBy:"最后修改人",lastModifiedTime:"最后修改时间",operation:"操作",viewDetails:"查看详情",edit:"编辑",initialization:"初始化",delete:"删除",selected:"已选",item:"项",query:"查询",pleaseEnter:"请输入模板名称",addDialogTitle:"添加模板",templateType:"模板类型",pleaseSelectType:"请选择模板类型",addSuccess:"添加成功",quit:"取 消",confirm:"确 认",deleteTitle:"删除模板",deleteContent1:"模板删除后不可恢复。",deleteContent2:"请问您确认要删除吗？",deleteSuccess:"删除成功",batchDisableChoosePrompt:"請選擇模板后再执行该操作",initializeTitle:"初始化模板",initializeContent1:"模板初始化后恢复初始内容。",initializeContent2:"请问您确认要初始化吗？",initializeSuccess:"模板初始化成功。"},scheduledTask:{scheduledTaskId:"仿真任务ID",idPlaceholder:"请输入仿真任务ID",type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工况名称",namePlaceholder:"请输入工况名称",execCycle:"执行周期",daily:"每日",weekly:"每周",monthly:"每月",immediateExec:"立即执行",pause:"暂停",column_ID:"仿真任务ID",column_type:"工况类型",simulationTime:"仿真对象时间",nextSimExecutionTime:"下次仿真执行时间",status:"当前状态",historicalCount:"历史仿真次数",operation:"操作",edit:"编辑",reStart:"重启",delete:"删除",selected:"已选",item:"项",query:"查询",pendingScheduling:"待调度",inProgress:"执行中",isDeleted:"已删除",dataMountingInProgress:"数据挂载中",dataMounted:"数据已挂载",monday:"星期一",tuesday:"星期二",wednesday:"星期三",thursday:"星期四",friday:"星期五",saturday:"星期六",sunday:"星期天",taskExecution:"任务执行",executionContinue:"是否立即执行当前选择的任务?",taskRestart:"任务重启",restartContinue:"是否立即重启当前选择的任务?",taskDelete:"任务删除",DeleteContinue:"是否删除当前选择的任务?",taskPause:"任务暂停",pauseContinue:"是否立即暂停当前选择的任务?",pleaseSelectTasks:"请选择任务后再执行操作",taskExecuted:"所选调度任务已开始执行",taskRestarted:"所选调度任务已重启",deleteSuccess:"删除成功",deleteFail:"删除失败",taskPaused:"所选调度任务已暂停"},taskDetail:{pleaseSelectCycleType:"请选择任务执行周期",pleaseSelectCycleTime:"请选择任务执行时间",pleaseSelectHistoryDataType:"请确定历史数据选择方式",pleaseSelectdcRecordId:"请选择直流仿真结果",saveSuccess:"修改成功",saveChanges:"确认提交",cancel:"取消",execCycle:"执行周期",daily:"每日",weekly:"每周",monthly:"每月",yearly:"每年",monthText:"月",dayText:"日",baseInfo:"基础信息",conditionName:"工况名称",creator:"创建人",conditionType:"工况类型",simulationTaskID:"仿真任务ID",simulationTasksNumber:"工况仿真任务数",simulationRecordsNumber:"工况仿真记录数",viewDetails:"查看详情",executeConfiguration:"执行配置",taskExecutionCycle:"任务执行周期",taskExecutionTime:"任务执行时间",expectNextSimTime:"下次仿真执行时间",simObjTime:"仿真对象时间",particleSize:"颗粒度",minutes10:"10分钟",dataMounting:"数据挂载",historicalDataSelectMethod:"历史数据选择方式",introduceInfo:"您设置的定时任务今后将会一直使用此区间的数据进行仿真，不会自动更新",customDataStartTime:"自定义数据开始时间",customDataEndTime:"自定义数据结束时间",historicalValid:"历史数据校验有效",historicalFailed:"历史数据校验失败",verifyMounting:"校验挂载",viewDataResults:"查看数据结果",selectLoadForecastingResults:"选择负荷预测结果",matchingDCSimulationResults:"匹配直流仿真结果",selectSimulationRecord:"选择仿真记录",remarks:"备注",th:"号",automaticallyMatch:"按周期自动匹配数据区间",customizeHistoricalDataInterval:"自定义历史数据区间",importResultsFromLoadForecasting:"从负荷预测导入结果",pleaseSelect:"请选择一条仿真记录",pleaseSelectResult:"请选择一条负荷预测结果"},result:{scheduledTaskId:"仿真任务ID",idPlaceholder:"请输入仿真任务ID",type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工况名称",namePlaceholder:"请输入工况名称",execCycle:"执行周期",daily:"每日",weekly:"每周",monthly:"每月",immediateExec:"立即执行",pause:"暂停",column_ID:"仿真任务ID",column_type:"工况类型",simulationTime:"仿真执行时间",nextSimExecutionTime:"下次仿真执行时间",status:"当前状态",historicalCount:"历史仿真次数",operation:"操作",edit:"编辑",reStart:"重启",delete:"删除",selected:"已选",item:"项",query:"查询",exportAll:"导出",simulationResultID:"仿真结果ID",conditionName:"工况名称",triggerMethod:"触发方式",executor:"执行人",systemTasks:"系统任务",taskID:"任务ID",simulationBegins:"仿真开始",simulationEnds:"仿真结束",simulationState:"仿真状态",currentTaskSimulationResults:"当前任务仿真结果",historicalSimulationFrequency:"历史仿真次数",stopSimulation:"停止仿真",viewDetails:"查看详情",resultExport:"结果导出",checkTheReason:"查看原因",retrySimulation:"重试仿真",automaticExecution:"自动执行",manuallyExecute:"手动执行",inProgress:"执行中",success:"成功",fail:"失败",simulationStopped:"工况仿真已经停止",simulationReExecuted:"仿真已重新执行",simulationResult:"仿真结果",simulationRecordTable:"仿真记录表",simTaskFail:"仿真任务失败",knowed:"知道了",confirmSimType:"确认仿真方式",confirmSimTypeMessage:"请确认是否以最新工况执行仿真?",viewCurrentSim:"查看此工况",execOriginSim:"执行原有工况",execNewSim:"执行新工况"},mgtDetail:{group:"组",open:"合闸",close:"闭闸",fast:"快车",slow:"慢车",success:"成功",fail:"失败",gmonth:"个月",month:"月",week:"周",day:"天",hour:"小时",minute:"分钟",second:"秒",ac:"交流",dc:"直流",autoExecute:"自动执行",manualExecute:"手动执行",filename:"文件名称",msg_filename_null:"请输入文件名后, 再提交",edit:"修改",cancelEidt:"取消修改",saveEidt:"保存修改",msg_edit_not_save:"请先保存, 或者取消",loadingText:"加载中",tablenNoDataText:"暂无数据",cancleText:"取消",confirmText:"确认",continueAdd:"继续添加",confirmData:"确认数据",dataResult:"数据结果",backReselect:"返回重选",syncConfig:"同步配置",autoResize:"自适应",updateTime:"更新时间",dataUpdateTime:"数据更新时间",conditionDetail:"工况详情",simResultList:"仿真结果列表",runSimulation:"执行仿真",runSimulationPromptText:"是否对当前的工况进行仿真?",simulationTaskSubmitted:"仿真任务已提交！",simulationTaskSubmittedPromptText:"仿真结束后，结果可在 {result} 中查看",scheduledTask:"新增定时任务",configSimParameters:"配置仿真参数",viewCondition:"查看工况讯息",search:"检索",search_placeholder:"请输入设备/电缆名称, 支持模糊搜索",search_placeholder2:"请输入站点, 支持模糊搜索",syncConfigData:"同步配置数据",syncPromptText:"系统会将您已修改的参数同步仿真配置数据，确认要继续吗?",undoAllChanges:"撤销全部修改",undoPromptText:"系统会将您已修改的参数撤销回之前的状态，确认要继续吗?",downloadConfigTemplate:"下载配置模板",importConfigFile:"导入配置文件",exportConfigFile:"导出配置文件",viewDetail:"查看详情",pleaseSelect:"请选择",customDataStartTime:"自定义数据开始时间",customDataEndTime:"自定义数据结束时间",checkLoad:"校验挂载",viewDataResult:"查看数据结果",startTime:"开始时间",endTime:"结束时间",endTimeLtStartTime:"结束时间不得小于开始时间",historyDataVerifySucc:"历史数据校验有效",historyDataVerifyFail:"历史数据校验失败",recalculate:"重新计算",saveSucc:"保存成功",saveFail:"保存失败",number:"数量",condition:{id:"工况ID",importLoadPredictParameters:"导入负荷预测参数",loadHistoryDataParameters:"挂载历史数据参数",selectForecastResult:"选择负荷预测结果",task:"任务",forecastStartTime:"预测起始时间",forecastEndTime:"预测结束时间",forecastDuration:"预测时长",granularity:"颗粒度",baseInfo:"工况基本信息",name:"工况名称",des:"工况描述",creator:"创建人",createTime:"创建时间",electType:"工况类型",associatedSimTask:"该工况关联仿真任务",associatedSimRecord:"该工况历史仿真记录"},template:{syncSucc:"同步成功",syncError:"同步失败"},result:{exportElecPicture:"导出电气图",exportSimResult:"导出仿真结果",exportSimAbnormalResult:"仅导出异常结果",exportConfigFile:"导出配置文件",showAlermMsg:"展示报警讯息",showEditedParams:"展示已修改参数",allParams:"全部参数",editedParams:"已修改参数",allData:"全部数据",isAbnormal:"是否异常",abnormalData:"异常数据",simResult:"仿真结果",presetThreshold:"预设阈值",viewConfigParams:"查看配置参数",viewSimResult:"查看仿真结果",viewSimInfo:"查看仿真讯息",editElecThreshold:"修改电气阈值",simBaseInfo:"仿真基本讯息",relatedCondition:"关联工况",conditionDes:"工况描述",conditionType:"工况类型",triggerMode:"触发方式",executor:"执行人",taskStartTime:"任务开始时间",taskEndTime:"任务结束时间",simDuration:"仿真时长",deviceCategory:"设备类型",resultLabel:"结果标签",overload:"超负荷",lowLod:"低负荷",deviceCable:"设备/电缆",device:"设备",cable:"电缆",result:"仿真结果",currentLoadThreshold:"仿真阈值",exceedRatio:"超限比例",reason:"故障原因",deviceCapacity:"设备容量",from:"故障來自",capacity:"容量",currentCapacity:"载流量",crossSection:"电缆截面",rtGroup:"RT开启数量"}},simTool:{edit_prop:"编辑",delete_prop:"删除",common:"通用",circuit:"电路",category:"类型",station:"站点",url:"线路",status:"状态",associate:"关联设备",please_input:"请输入",on:"开启",off:"关闭",canvasConfig:"画布配置",canvas:"画布",size:"大小",color:"颜色",scale:"缩放",bg_color:"背景颜色",anchor:"{status}锚点",grid:"{status}网格",rotate:"{status}旋转",lineStyle:"连线方式",line:"直线",polyline:"折线",curvedline:"曲线",watermark:"水印",content:"内容",degree:"角度",attribute:"属性",shape:"图形",width:"宽",height:"高",zIndex:"层级",data:"数据",device:"设备编号",name:"名称",addData:"添加数据",attribute_name:"属性名",attribute_value:"属性值",attribute_alias:"属性别名",isNotNull:"{name}不可为空",isNotRepeat:"{name}已存在, 不可重复添加",appearance:"外观",fill:"填充颜色",opacity:"不透明度",stroke:"线条颜色",stroke_style:"线条样式",stroke_width:"线条宽度",stroke_opacity:"线条不透明度",radius:"半径",arrow:"{status}箭头",animation:"{status}动画",in_line:"入线",out_line:"出线",let_auto:"默认",has_line:"有连线",text:"文本",text_placeholder:"请输入文本",font_size:"字号",font_family:"字体",line_height:"行高",pipe:"管道"},topNavigation:{accident:"事故",abnormal:"不正常的",notification:"通知",pendingStatus:"待处理",processedStatus:"已处理",viewDetails:"查看更多"},overview:{title:"港铁电气系统图",ac_electric:"AC交流线路",dc_electric:"DC直流线路",input_placeholder:"设备名称",acknowledge:"确认",dealPromptly:"详情",point:"点位",name:"名称",fault_startTime:"故障时间",fault_duration:"故障持续时间",equ_location:"设备位置",fault_description:"故障描述",fault_Date:"故障时间",recovery_advice:"恢复建议",promptText1:"权限不够"},mount:{title:"数据挂载时间",description1:"仿真任务执行开始之前",description2:"小时 (范围: 2-22)",cancel:"取消",saveChanges:"保存",saveSucces:"保存成功",prompt:"请输入2-22正整数"}}},a={golbal:{projectName:"智慧供電孿生系統",cn:"簡體",tc:"繁體",en:"英文",paginationLabel1:"第",paginationLabel2:"條",paginationLabel3:"總共",paginationLabel4:"條/頁",status1:"停用",status2:"啟用",status3:"不限",status4:"啟用",btnText1:"取消",btnText2:"確認",btnText3:"繼續",btnText4:"保存",dialogPrompt1:"確認要繼續麼?",placeholder1:"請輸入",logout:"退出登錄",mustEnter:" 表示必填",dialogPrompt2:"最大字符限制為{size}個字符",requestTimeout:"網絡連接超時,請檢查您的網絡連接。如果問題仍然存在,請聯系技術支持。"},routes:{404:"404",simulationMGT:"仿真與故障恢復建議",SIM:"仿真與故障恢復建議",operatingCondition:"工況列表",scheduledTask:"定時仿真任務列表",taskDetail:"仿真任務配寘",result:"仿真結果列表",electricalThreshold:"仿真電氣阈值",templateMGT:"仿真模板管理",viewDetail:"查看詳情",editDetail:"编辑詳情",operatingConditionViewDetail:"查看詳情",operatingConditionEditDetail:"编辑詳情",resultViewDetail:"查看詳情",templateMGTViewDetail:"查看詳情",templateMGTEditDetail:"編輯詳情",overview:"綜合概覽",overviewEnergy:"綜合概覽",mount:"仿真掛載設置",SIMSetting:"仿真設置"},page:{error404:{title:"出錯啦",subTitle:"親愛的用戶,您訪問的頁面不存在..."},electricalThreshold:{min:"最小值",max:"最大值",switch_status:"打開/關閉狀態（1關閉，0打開）",input_placeholder:"請輸入站點, 支持模糊檢索",import_file:"導入文件",export_file:"導出文件",titleAC:"交流AC閾值",titleDC:"直流DC閾值",tip:"不填視爲無閾值 ",btnText1:"取消",btnText2:"保存修改",vbus:"母線電壓",re:"再生能量捕獲裝置",rt:"整流機組馈线{no}",st:"配電變壓器饋線{no}",irt:"整流機組饋線電流",ist:"配電變饋線電流 ",ire:"再生設備饋線電流",icable:"電纜電流",load:"饋電變壓器負載功率",iout:"輸出電流",tss:"牽引變電所",utLeft:"上行軌道左側直流饋線斷路器電流",dtLeft:"下行軌道左側直流饋線斷路器電流",utRight:"上行軌道右側直流饋線斷路器電流",dtRight:"下行軌道右側直流饋線斷路器電流",nfeeder:"負饋線均方根電流",rtPower15s:"整流變壓器15s功率的均方根值",rtPower60s:"整流變壓器60s功率的均方根值",rtPowerLt:"整流變壓器長期功率的均方根值",rePower10s:"再生能量捕獲裝置10s功率的均方根值",rePower60s:"再生能量捕獲裝置60s功率的均方根值",rePowerLt:"再生能量捕獲裝置長期功率的均方根值",vnet:"架空線電壓",vnetMin:"架空線電壓, 最小值",vnetMax:"架空線電壓, 最大值",rail:"軌道電位",railMin:"軌道電位, 最小值",railMax:"軌道電位, 最大值",ovpd:"OVPD 均方根電流",ovpdRms:"OVPD 均方根電流",ovpdMin:"OVPD 均方根電流，最小值",ovpdMax:"OVPD 均方根電流，最大值",ovpdPeak:"OVPD 峰值電流",ovpdPeakMin:"OVPD 峰值電流，最小值",ovpdPeakMax:"OVPD 峰值電流，最大值",utbypass:"上行軌道旁路隔離器開閉狀態",dtbypass:"下行軌道旁路隔離器開閉狀態",powerP:"有功功率",powerQ:"無功功率",rtPowerP:"整流機組{no}有功功率",rtPowerQ:"整流機組{no}無功功率",stPowerP:"配電變壓器{no}有功功率",stPowerQ:"配電變壓器{no}無功功率",rePowerP:"再生能量捕獲裝置有功功率",rePowerQ:"再生能量捕獲裝置無功功率",number:"整流機組數量",RT_EFF:"整流機組效率",RE_EFF:"再生能量捕獲裝置效率",ST_EFF:"配電變壓器效率",tip1:"请检查, 最小值不得超過最大值。",tip2:"值不能為空！"},electricalParams:{ovpd:"軌道電位限制裝置（OVPD）开关状态",utLeft:"上行軌道左側直流饋線斷路器开关状态",dtLeft:"下行軌道左側直流饋線斷路器开关状态",utRight:"上行軌道右側直流饋線斷路器开关状态",dtRight:"下行軌道右側直流饋線斷路器开关状态",nfeeder:"負饋線均方根开关状态"},operatingCondition:{type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工況名稱",namePlaceholder:"請輸入工況名稱，支持模糊檢索",triggerMode:"觸發管道",simulationStatus:"仿真狀態",createBy:"創建人",createByPlaceholder:"請輸入創建人",addCondition:"新增工況",runSimulation:"執行仿真",copyCondition:"複製工況",deleteCondition:"删除工況",conditionId:"工況ID",faultId:"故障ID",column_name:"工況名稱",column_type:"类型",creator:"創建人",creationTime:"創建時間",lastModifiedBy:"最後修改人",lastModifiedTime:"最後修改時間",historicalCount:"歷史仿真次數",simJobTotal:"當前任務數",operation:"操作",viewDetails:"查看詳情",configScheduleTask:"配置定時任務",viewTask:"查看任務",selected:"已選",item:"项",query:"查詢",pleaseEnter:"請輸入工況名稱",pleaseFaultId:"請輸入故障ID",pleaseSelectType:"請選擇工況類型",addDialogTitle:"新增工況",remark:"描述",pleaseEnterRemark:"請輸入工況描述",templateType:"工況類型",template:"工況模板",pleaseSelectTemplate:"請選擇工況模板",addSuccess:"添加成功",addFail:"添加失败",quit:"取 消",confirm:"確 認",batchDisableChoosePrompt:"請選擇工況后再执行该操作",deleteTitle:"永久删除工況",deleteContent1:"工況刪除後不可恢復,",deleteContent2:"確認要繼續嗎?",deletePromptText:"該工況已永久刪除",batchDisableTitle:"批量删除工況",batchDisableContent1:"選中工況刪除後不可恢復,",batchDisablePromptText:"選中工況已刪除",edit:"編輯",confirmAgain:"再次確認，",isContinue:"是否對當前選擇的工況執行仿真?",simulationExecuted:"工況仿真已經執行",executionException:"執行异常",originalCondition:"原工況",newConditionName:"新工況名稱"},templateMGT:{type:"交流/直流",noLimit:"不限",type1:"交流/AC",type2:"直流/DC",name:"模板名稱",namePlaceholder:"請輸入模板名稱，支持模糊檢索",createBy:"創建人",createByPlaceholder:"請輸入創建人",caseNumber:"案例編號",SchemaTemplateVoPlaceholder:"請輸入案例編號",sceneType:"場景類型",scence:"運營場景",scencePlaceholder:"請輸入運營場景",addTemplate:"添加模板",deleteTemplate:"删除模板",noPlaceholder:"請選擇案例編號",scenarioTypePlaceholder:"請選擇場景類型",operationScenarioPlaceholder:"請輸入操作場景",column_name:"模板名稱",column_type:"类型",column_no:"用例編號",column_scenarioType:"場景類型",column_operationScenario:"操作場景",column_state:"狀態",creator:"創建人",creationTime:"創建時間",lastModifiedBy:"最後修改人",lastModifiedTime:"最後修改時間",state:"狀態",enable:"可用",disable:"禁用",operation:"操作",viewDetails:"查看詳情",edit:"編輯",initialization:"初始化",delete:"删除",selected:"已選",item:"项",query:"查詢",pleaseEnter:"請輸入模板名稱",pleaseSelectType:"請選擇模板類型",addDialogTitle:"添加模板",templateType:"模板類型",addSuccess:"添加成功",quit:"取 消",confirm:"確 認",deleteTitle:"删除模板",deleteContent1:"模板删除後不可恢復。",deleteContent2:"請問您確認要删除嗎？",deleteSuccess:"删除成功",batchDisableChoosePrompt:"請選擇模板后再执行该操作",initializeTitle:"初始化模板",initializeContent1:"模板初始化後恢復初始內容。",initializeContent2:"請問您確認要初始化嗎？",initializeSuccess:"模板初始化成功。"},scheduledTask:{scheduledTaskId:"仿真任務ID",idPlaceholder:"請輸入仿真任務ID",type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工況名稱",namePlaceholder:"請輸入工況名稱",execCycle:"執行週期",daily:"每日",weekly:"每週",monthly:"每月",immediateExec:"立即執行",pause:"暫停",column_ID:"仿真任務ID",column_type:"工況类型",simulationTime:"仿真對象時間",nextSimExecutionTime:"下次仿真執行時間",status:"當前狀態",historicalCount:"歷史仿真次數",operation:"操作",edit:"編輯",reStart:"重啓",delete:"刪除",selected:"已選",item:"项",query:"查詢",pendingScheduling:"待調度",inProgress:"執行中",isDeleted:"已刪除",dataMountingInProgress:"數據掛載中",dataMounted:"數據已掛載",monday:"星期一",tuesday:"星期二",wednesday:"星期三",thursday:"星期四",friday:"星期五",saturday:"星期六",sunday:"星期天",taskExecution:"任務執行",executionContinue:"是否立即執行當前選擇的任務?",taskRestart:"任務重啓",restartContinue:"是否立即重啓當前選擇的任務?",taskDelete:"任務删除",DeleteContinue:"是否删除當前選擇的任務?",taskPause:"任務暫停",pauseContinue:"是否立即暫停當前選擇的任務?",pleaseSelectTasks:"請選擇任務後再執行操作",taskExecuted:"所選調度任務已開始執行",taskRestarted:"所選調度任務已重啓",deleteSuccess:"删除成功",deleteFail:"删除失敗",taskPaused:"所選調度任務已暫停"},taskDetail:{pleaseSelectCycleType:"請選擇任務執行週期",pleaseSelectCycleTime:"請選擇任務執行時間",pleaseSelectHistoryDataType:"請確定歷史數據選擇方式",pleaseSelectdcRecordId:"請選擇直流仿真結果",saveSuccess:"修改成功",saveChanges:"确认提交",cancel:"取消",execCycle:"Exec. Cycle",daily:"每日",weekly:"每週",monthly:"每月",yearly:"每年",monthText:"月",dayText:"日",baseInfo:"基礎信息",conditionName:"工況名稱",creator:"創建人",conditionType:"工況類型",simulationTaskID:"仿真任務ID",simulationTasksNumber:"工況仿真任務數",simulationRecordsNumber:"工況仿真記錄數",viewDetails:"查看詳情",executeConfiguration:"執行配寘",taskExecutionCycle:"任務執行週期",taskExecutionTime:"任務執行時間",expectNextSimTime:"下次仿真執行時間",simObjTime:"仿真對象時間",particleSize:"顆粒度",minutes10:"10分鐘",dataMounting:"數據掛載",historicalDataSelectMethod:"歷史數據選擇方式",introduceInfo:"您設置的定時任務今後將會一直使用此區間的數據進行仿真，不會自動更新！",customDataStartTime:"自定義數據開始時間",customDataEndTime:"自定義數據結束時間",historicalValid:"歷史數據校驗有效",historicalFailed:"歷史數據校驗失敗",verifyMounting:"校驗掛載",viewDataResults:"查看數據結果",selectLoadForecastingResults:"選擇負荷預測結果",matchingDCSimulationResults:"匹配直流仿真結果",selectSimulationRecord:"選擇仿真記錄",remarks:"備註",th:"號",automaticallyMatch:"按週期自動匹配數據區間",customizeHistoricalDataInterval:"自定義歷史資料區間",importResultsFromLoadForecasting:"從負荷預測導入結果",pleaseSelect:"請選擇一條仿真記錄",pleaseSelectResult:"請選擇一條負荷預測結果"},result:{scheduledTaskId:"仿真任務ID",idPlaceholder:"請輸入仿真任務ID",type:"交流/直流",noLimit:"不限",type1:"交流AC",type2:"直流DC",name:"工况名稱",namePlaceholder:"請輸入工况名稱",execCycle:"執行週期",daily:"每日",weekly:"每週",monthly:"每月",immediateExec:"立即執行",pause:"暫停",column_ID:"仿真任務ID",column_type:"工况类型",simulationTime:"仿真執行時間",nextSimExecutionTime:"下次仿真執行時間",status:"當前狀態",historicalCount:"歷史仿真次數",operation:"操作",edit:"編輯",reStart:"重啓",delete:"删除",selected:"已選",item:"項",query:"査詢",exportAll:"導出",simulationResultID:"仿真结果ID",conditionName:"工况名稱",triggerMethod:"觸發方式",executor:"執行人",systemTasks:"系統任務",taskID:"任務ID",simulationBegins:"仿真開始",simulationEnds:"仿真結束",simulationState:"仿真狀態",currentTaskSimulationResults:"當前任務仿真結果",historicalSimulationFrequency:"歷史仿真次數",stopSimulation:"停止仿真",viewDetails:"查看詳情",resultExport:"結果導出",checkTheReason:"查看原因",retrySimulation:"重試仿真",automaticExecution:"自動執行",manuallyExecute:"手動執行",inProgress:"執行中",success:"成功",fail:"失败",simulationStopped:"工况仿真已經停止",simulationReExecuted:"仿真已重新執行",simulationResult:"仿真结果",simulationRecordTable:"仿真記錄表",simTaskFail:"仿真任務失败",knowed:"知道了",confirmSimType:"确认仿真方式",confirmSimTypeMessage:"您當前操作的工況內容已發生變更。請確認是否以最新工況執行仿真? ",viewCurrentSim:"查看此工況",execOriginSim:"執行原有工況",execNewSim:"執行新工況"},mgtDetail:{group:"組",open:"合閘",close:"閉閘",fast:"快車",slow:"慢車",success:"成功",fail:"失敗",gmonth:"個月",month:"月",week:"周",day:"天",hour:"小時",minute:"分鐘",second:"秒",ac:"交流",dc:"直流",autoExecute:"自動執行",manualExecute:"手動執行",filename:"文件名稱",msg_filename_null:"請輸入文件名後, 再提交",edit:"修改",cancelEidt:"取消修改",saveEidt:"保存修改",msg_edit_not_save:"請先保存, 或者取消",loadingText:"加載中",tablenNoDataText:"暫無數據",cancleText:"取消",confirmText:"確認",continueAdd:"繼續添加",confirmData:"確認數據",dataResult:"數據結果",backReselect:"返回重選",syncConfig:"同步配置",autoResize:"自適應",updateTime:"更新時間",dataUpdateTime:"數據更新時間",conditionDetail:"工況詳情",simResultList:"仿真結果列表",runSimulation:"執行仿真",runSimulationPromptText:"是否對當前的工況進行仿真?",simulationTaskSubmitted:"仿真任務已提交！",simulationTaskSubmittedPromptText:"仿真結束後，結果可在 {result} 中查看",scheduledTask:"新增定時任務",configSimParameters:"配置仿真參數",viewCondition:"查看工況訊息",search:"檢索",search_placeholder:"請輸入設備/電纜名稱, 支持模糊搜索",search_placeholder2:"請輸入站点, 支持模糊搜索",syncConfigData:"同步配置數據",syncPromptText:"系統會將您已修改的參數同步仿真配置數據，确认要继续吗?",undoAllChanges:"撤銷全部修改",undoPromptText:"系統會將您已修改的參數撤銷回之前的狀態，确认要继续吗?",downloadConfigTemplate:"下載配置模板",importConfigFile:"導入配置文件",exportConfigFile:"導出配置文件",viewDetail:"查看詳情",pleaseSelect:"請選擇",customDataStartTime:"自定義數據開始時間",customDataEndTime:"自定義數據結束時間",checkLoad:"校驗掛載",viewDataResult:"查看數據結果",startTime:"開始時間",endTime:"結束時間",endTimeLtStartTime:"結束時間不得小於開始時間",historyDataVerifySucc:"歷史數據校驗有效",historyDataVerifyFail:"歷史數據校驗失敗",recalculate:"重新計算",saveSucc:"保存成功",saveFail:"保存失敗",number:"數量",condition:{id:"工況ID",importLoadPredictParameters:"導入負荷預測參數",loadHistoryDataParameters:"掛載歷史數據參數",selectForecastResult:"選擇負荷預測結果",task:"任務",forecastStartTime:"預測起始時間",forecastEndTime:"預測結束時間",forecastDuration:"預測時長",granularity:"顆粒度",baseInfo:"工況基本信息",name:"工況名稱",des:"工況描述",creator:"創建人",createTime:"創建時間",electType:"工況類型",associatedSimTask:"該工況關聯仿真任務",associatedSimRecord:"該工況歷史仿真記錄"},template:{syncSucc:"同步成功",syncError:"同步失敗"},result:{exportElecPicture:"導出電氣圖",exportSimResult:"導出仿真結果",exportSimAbnormalResult:"僅導出異常結果",exportConfigFile:"導出配置文件",showAlermMsg:"展示報警訊息",showEditedParams:"展示已修改參數",allParams:"全部參數",editedParams:"已修改參數",allData:"全部數據",isAbnormal:"是否異常",abnormalData:"異常數據",simResult:"仿真結果",presetThreshold:"預設閾值",viewConfigParams:"查看配置參數",viewSimResult:"查看仿真結果",viewSimInfo:"查看仿真訊息",editElecThreshold:"修改電氣閾值",simBaseInfo:"仿真基本訊息",relatedCondition:"關聯工況",conditionDes:"工況描述",conditionType:"工況類型",triggerMode:"觸發方式",executor:"執行人",taskStartTime:"任務開始時間",taskEndTime:"任務結束時間",simDuration:"仿真時長",deviceCategory:"設備類型",resultLabel:"結果標籤",overload:"超負荷",lowLod:"低負荷",deviceCable:"設備/電纜",device:"設備",cable:"電纜",result:"仿真結果",currentLoadThreshold:"仿真閾值",exceedRatio:"超限比例",reason:"故障原因",deviceCapacity:"设备容量",from:"故障来自",capacity:"容量",currentCapacity:"載流量",crossSection:"電纜截面",rtGroup:"RT开启数量"}},simTool:{edit_prop:"編輯",delete_prop:"删除",common:"通用",circuit:"電路",category:"類型",station:"站點",url:"線路",status:"狀態",associate:"關聯設備",please_input:"請輸入",on:"開啟",off:"關閉",canvasConfig:"畫布配置",canvas:"畫布",size:"大小",color:"顏色",scale:"縮放",bg_color:"背景顏色",anchor:"{status}錨點",grid:"{status}網格",rotate:"{status}旋轉",lineStyle:"連線樣式",line:"直線",polyline:"折線",curvedline:"曲線",watermark:"水印",degree:"角度",content:"內容",attribute:"屬性",shape:"圖形",width:"寬",height:"高",zIndex:"層級",data:"數據",device:"設備編號",name:"名稱",addData:"添加數據",attribute_name:"屬性名",attribute_value:"屬性值",attribute_alias:"屬性別名",isNotNull:"{name}不可為空",isNotRepeat:"{name}已存在, 不可重復添加",appearance:"外觀",fill:"填充顏色",opacity:"不透明度",stroke:"線條顏色",stroke_style:"線條樣式",stroke_width:"線條寬度",stroke_opacity:"線條不透明度",radius:"半徑",arrow:"{status}箭頭",animation:"{status}動畫",in_line:"入線",out_line:"出線",let_auto:"默認",has_line:"有連線",text:"文本",text_placeholder:"請輸入文本",font_size:"字號",font_family:"字體",line_height:"行高"},topNavigation:{accident:"事故",abnormal:"不正常的",notification:"通知",pendingStatus:"待處理",processedStatus:"已處理",viewDetails:"查看更多"},overview:{title:"港鐵電氣系統圖",ac_electric:"AC交流線路",dc_electric:"DC直流線路",input_placeholder:"设备名称",acknowledge:"確認",dealPromptly:"詳情",point:"點位",name:"名稱",fault_startTime:"故障時間",fault_duration:"故障持續時間",equ_location:"设备位置",fault_description:"故障描述",fault_Date:"故障时间",recovery_advice:"恢复建议",promptText1:"權限不夠"},mount:{title:"數據掛載時間",description1:"仿真任務執行開始之前",description2:"小時 (範圍: 2-22)",cancel:"取消",saveChanges:"保存",saveSucces:"保存成功",prompt:"請輸入2-22正整數"}}},s={golbal:{projectName:"Digital Twins System (Power supply)",cn:"Simplified Chinese",tc:"Traditional Chinese",en:"English",paginationLabel1:"",paginationLabel2:"",paginationLabel3:"",paginationLabel4:"Pieces/Page",status1:"Disabled",status2:"Normal",status3:"Unlimited",status4:"Enabled",btnText1:"Cancel",btnText2:"Confirm",btnText3:"Confirm",btnText4:"Save",dialogPrompt1:"Confirm to proceed?",placeholder1:"Please Enter",logout:"Log Out",mustEnter:" must be entered",dialogPrompt2:"The maximum character limit is {size} characters.",requestTimeout:"Network connection timed out, please check your network connection. If the problem persists, contact technical support."},routes:{404:"404",simulationMGT:"Simulation",SIM:"Simulation",operatingCondition:"SIM Operating Condition",scheduledTask:"SIM Scheduled Task",taskDetail:"Simulation Task Configuration",result:"SIM Result",electricalThreshold:"SIM Threshold Setting",templateMGT:"SIM Template Setting",viewDetail:"View Detail",editDetail:"Edit Detail",operatingConditionViewDetail:"View Detail",operatingConditionEditDetail:"Edit Detail",resultViewDetail:"View Detail",templateMGTViewDetail:"View Detail",templateMGTEditDetail:"Edit Detail",overview:"Visualization",overviewEnergy:"Visualization",mount:"SIM Load Setting",SIMSetting:"SIM Setting"},page:{error404:{title:"There's been a mistake.",subTitle:"The page you are looking for does not exist."},electricalThreshold:{min:"min",max:"max",switch_status:"open/closed（1: closed，0: open）",input_placeholder:"Please Enter Station, Support Fuzzy Search.",import_file:"Import",export_file:"Export",titleAC:"AC Threshold",titleDC:"DC Threshold",tip:"Blank as No Threshold",btnText1:"CANCEL",btnText2:"Save Changes",vbus:"Busbar Voltage, V {type}",re:"Regenerative Energy Capture Device Feeder",rt:"Rectifier Transformer Unit {no} Feeder",st:"Service Transformer {no} Feeder",irt:"Current of Rectifier Transformer Unit {no} Feeder, A {type}",ist:"Current of Service Transformer {no} Feeder, A {type}",ire:"Current of Regenerative Energy Capture Device Feeder, A {type}",icable:"Current of Ring Cable, A {type}",load:"Load of Infeed Transformer, MVA {type}",iout:"Current Output of Infeed Transformer, A {type}",tss:"Traction Substation",utLeft:"rms Current of Up Track DCCB (Left), A {type}",dtLeft:"rms Current of Down Track DCCB (Left), A {type}",utRight:"rms Current of Up Track DCCB (Right), A {type}",dtRight:"rms Current of Down Track DCCB (Right), A {type}",nfeeder:"rms Current of Negative Feeder, A {type}",rtPower15s:"15s rms Power Output of Rectifier Transformer Unit,kW {type}",rtPower60s:"60s rms Power Output of Rectifier Transformer Unit,kW {type}",rtPowerLt:"Long Term rms Power Output of Rectifier Transformer Unit,kW {type}",rePower10s:"10s rms Power Output of Regenerative Energy Capture Device,kW {type}",rePower60s:"60s rms Power Output of Regenerative Energy Capture Device,kW {type}",rePowerLt:"Long Term rms Power Output of Regenerative Energy Capture Device,kW {type}",vnet:"OHL Voltage, V {type}",rail:"Rail Potential, V {type}",ovpd:"The rms value of the current flow through a OVPD.",ovpdRms:"OVPD rms Current, A {type}",ovpdPeak:"OVPD Peak Current, A {type}",utbypass:"The open/closed status of the bypass isolator of up track (1 is closed, 0 is open).",dtbypass:"The open/closed status of the bypass isolator of down track (1 is closed, 0 is open).",powerP:"Wattful Power",powerQ:"Wattless Power",rtPowerP:"Active Power of Rectifier Transformer Unit {no} Feeder",rtPowerQ:"Reactive Power of Rectifier Transformer Unit {no} Feeder",stPowerP:"Active Power of Service Transformer {no} Feeder",stPowerQ:"Reactive Power of Service Transformer {no} Feeder",rePowerP:"Active Power of Regenerative Energy Capture Device Feeder",rePowerQ:"Reactive Power Regenerative Energy Capture Device Feeder",number:"Rectifier Transform Unit Quantity",RT_EFF:"Efficiency of Rectifier Transformer Unit",RE_EFF:"Efficiency of Regenerative Energy Capture Device",ST_EFF:"Efficiency of Service Transformer",tip1:"Please check that, The minimum value should not be greater than the maximum value.",tip2:"The value Cannot be empty."},electricalParams:{ovpd:"Switch status of the Overvoltage Protection Device (OVPD)",utLeft:"Up Track DCCB (Left)",dtLeft:"Down Track DCCB (Left)",utRight:"Up Track DCCB (Right)",dtRight:"Down Track DCCB (Right)",nfeeder:"Negative Feeder"},operatingCondition:{type:"AC/DC",noLimit:"All",type1:"Alternating Current AC",type2:"Direct Current DC",name:"Condition Name",namePlaceholder:"Please enter the Operating Condition name.",triggerMode:"Trigger Mode",simulationStatus:"Simulation Status",createBy:"Creator",createByPlaceholder:"Please enter the creator.",addCondition:"Add Condition",runSimulation:"Run Simulation",copyCondition:"Copy Condition",deleteCondition:"Delete Condition",conditionId:"Operating Condition ID",faultId:"Fault ID",column_name:"Name",column_type:"Type",creator:"Creator",creationTime:"Creation Time",lastModifiedBy:"Last Modified By",lastModifiedTime:"Last Modified Time",historicalCount:"Historical Simulation Count",simJobTotal:"Current Task Count",operation:"Operation",viewDetails:"View Details",configScheduleTask:"Config Schedule Task",viewTask:"View Task",selected:"Selected",item:"items",query:"Search",pleaseEnter:"Please enter the operating condition name.",pleaseFaultId:"Please enter the fault ID.",pleaseSelectType:"Please select the operating condition type.",addDialogTitle:"Add Operating Condition",templateType:"Type",remark:"Description",pleaseEnterRemark:"Please enter the operating condition description.",template:"Template",pleaseSelectTemplate:"Please select the template.",addSuccess:"Add Success",addFail:"Add Fail",quit:"Cancel",confirm:"Confirm",batchDisableChoosePrompt:"Please select at least one operating condition before performing this operation.",deleteTitle:"Permanent Delete The Operating Condition",deleteContent1:"Deleting the operating condition is irreversible.",deleteContent2:"Are you sure you want to delete it?",deletePromptText:"The operating condition Permanently Deleted.",batchDisableTitle:"Batch Delete Operating Condition",batchDisableContent1:"Deleting the selected operating condition is irreversible.",batchDisablePromptText:"The selected operating condition is deleted.",edit:"Edit",confirmAgain:"Confirm Again,",isContinue:"Whether to perform simulation on the currently selected operating condition?",simulationExecuted:"The working condition simulation has been executed.",executionException:"Execution exception",originalCondition:"Original Condition",newConditionName:"New Condition Name"},templateMGT:{type:"AC/DC",noLimit:"All",type1:"Alternating Current/AC",type2:"Direct Current/DC",name:"Template Name",namePlaceholder:"Please enter the template name.",createBy:"Creator",createByPlaceholder:"Please enter the creator.",caseNumber:"Case No.",SchemaTemplateVoPlaceholder:"Please enter the Case No. .",sceneType:"Scenario Type",scence:"Operation Scenario",scencePlaceholder:"Please enter the Operation Scenario",addTemplate:"Add Temp.",deleteTemplate:"Delete Temp.",noPlaceholder:"Please select the case number.",scenarioTypePlaceholder:"Please select a scenario type.",operationScenarioPlaceholder:"Please enter the operation scenario.",column_name:"Template Name",column_type:"Temp. Type",column_no:"Case No.",column_scenarioType:"Scenario Type",column_operationScenario:"Operation Scenario",column_state:"State",creator:"Creator",creationTime:"Creation Time",lastModifiedBy:"Last Modified By",lastModifiedTime:"Last Modified Time",state:"State",enable:"enable",disable:"disable",operation:"Operation",viewDetails:"View Details",edit:"Edit",initialization:"Initialize",delete:"Delete",selected:"Selected",item:"Items",query:"Search",pleaseEnter:"Please enter the template name.",pleaseSelectType:"Please select the template type.",addDialogTitle:"Add Template",templateType:"Temp. Type",addSuccess:"Add Success",quit:"Cancel",confirm:"Confirm",deleteTitle:"Delete Template",deleteContent1:"Deleting the template is irreversible.",deleteContent2:"Are you sure you want to delete it?",deleteSuccess:"Delete Success",batchDisableChoosePrompt:"Please select at least one template before performing this operation.",initializeTitle:"Initialization Template",initializeContent1:"Restore the initial content after the template is initialized.",initializeContent2:"Are you sure you want to initialize?",initializeSuccess:"The template initialization was successful."},scheduledTask:{scheduledTaskId:"Scheduled Task ID",idPlaceholder:"Please enter the scheduled Task ID.",type:"AC/DC",noLimit:"All",type1:"Alternating Current AC",type2:"Direct Current DC",name:"Name",namePlaceholder:"Please enter the task name.",execCycle:"Exec. Cycle",daily:"Daily",weekly:"Weekly",monthly:"Monthly",immediateExec:"Immediate Exec.",pause:"Pause",column_ID:"Scheduled Task ID",column_type:"Type",simulationTime:"Simulation Time",nextSimExecutionTime:"Next SIM Execution Time",status:"Status",historicalCount:"Historical Sim. Count",operation:"Operation",edit:"Edit",reStart:"ReStart",delete:"Delete",selected:"Selected",item:"items",query:"Search",pendingScheduling:"Pending for Execution",inProgress:"In Progress",isDeleted:"Is Deleted",dataMountingInProgress:"Data Loading In Progress",dataMounted:"Data Loaded",monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday",taskExecution:"Task Execution",executionContinue:"Do you want to execute the currently selected task immediately?",taskRestart:"Task Restart",restartContinue:"Do you want to restart the currently selected task immediately?",taskDelete:"Task Delete",DeleteContinue:"Do you want to delete the currently selected task?",taskPause:"Task Pause",pauseContinue:"Do you want to pause the currently selected task immediately?",pleaseSelectTasks:"Please select the task before executing the operation.",taskExecuted:"The selected scheduling task has started execution.",taskRestarted:"The selected scheduling task has been restarted.",deleteSuccess:"Delete Success",deleteFail:"Delete Fail",taskPaused:"The selected scheduling task has been paused."},taskDetail:{pleaseSelectCycleType:"Please select the task execution cycle.",pleaseSelectCycleTime:"Please select the task execution time.",pleaseSelectHistoryDataType:"Please select the history data type.",pleaseSelectdcRecordId:"Please select the DC simulation result.",saveSuccess:"Update Success",saveChanges:"Confirm Submit",cancel:"Cancel",execCycle:"Exec. Cycle",daily:"Daily",weekly:"Weekly",monthly:"Monthly",yearly:"Yearly",monthText:"",dayText:"th",baseInfo:"Basic Information",conditionName:"Condition Name",creator:"Creator",conditionType:"Condition Type",simulationTaskID:"Simulation Task ID",simulationTasksNumber:"Number of Simulation Tasks for the Condition",simulationRecordsNumber:"Number of Simulation Records for the Condition",viewDetails:"View Details",executeConfiguration:"Execute Configuration",taskExecutionCycle:"Task Execution Cycle",taskExecutionTime:"Task Execution Time",expectNextSimTime:"Next Simulation Execution Time",simObjTime:"Simulation Time",particleSize:"Granularity",minutes10:"10 minutes",dataMounting:"Data Loading",historicalDataSelectMethod:"Historical Data Selection Method",introduceInfo:"The scheduled tasks you have set will continue to use data from this interval for simulation in the future and will not be automatically updated.",customDataStartTime:"Custom Data Start Time",customDataEndTime:"Custom Data End Time",historicalValid:"Historical data verification is valid.",historicalFailed:"Historical data verification failed.",verifyMounting:"Verify Loading",viewDataResults:"View Data Results",selectLoadForecastingResults:"Select Load Forecasting Results",matchingDCSimulationResults:"Matching DC Simulation Results",selectSimulationRecord:"Select Simulation Record",remarks:"Remarks",th:"th",automaticallyMatch:"Automatically Match Data Intervals On A Periodic Basis",customizeHistoricalDataInterval:"Customize Historical Data Interval",importResultsFromLoadForecasting:"Import Results From Load Forecasting",pleaseSelect:"Please select a simulation record.",pleaseSelectResult:"Please select a load forecast result."},result:{scheduledTaskId:"Scheduled Task ID",idPlaceholder:"Please enter the scheduled Task ID.",type:"AC/DC",noLimit:"All",type1:"Alternating Current AC",type2:"Direct Current DC",name:"Name",namePlaceholder:"Please enter the task name.",execCycle:"Exec. Cycle",daily:"Daily",weekly:"Weekly",monthly:"Monthly",immediateExec:"Immediate Exec.",pause:"Pause",column_ID:"Scheduled Task ID",column_type:"Type",simulationTime:"Simulation Time",nextSimExecutionTime:"Next SIM Execution Time",status:"Status",historicalCount:"Historical Sim. Count",operation:"Operation",edit:"Edit",reStart:"ReStart",delete:"Delete",selected:"Selected",item:"Items",query:"Search",exportAll:"Export",simulationResultID:"Simulation Result ID",conditionName:"Condition Name",triggerMethod:"Trigger Method",executor:"Executor",systemTasks:"System Tasks",taskID:"Task ID",simulationBegins:"Simulation Begins",simulationEnds:"Simulation Ends",simulationState:"Simulation State",currentTaskSimulationResults:"Current Task Simulation Results",historicalSimulationFrequency:"Historical Simulation Frequency",stopSimulation:"Stop Simulation",viewDetails:"View Details",resultExport:"Result Export",checkTheReason:"Check The Reason",retrySimulation:"Retry Simulation",automaticExecution:"Automatic Execution",manuallyExecute:"Manually Execute",inProgress:"In Progress",success:"Success",fail:"Fail",simulationStopped:"The simulation of working conditions has stopped.",simulationReExecuted:"Simulation has been re executed.",simulationResult:"simulation Result ",simulationRecordTable:"Simulation Record Table",simTaskFail:"Simulation Task Failed",knowed:"Got It",confirmSimType:"Confirm Simulation Method",confirmSimTypeMessage:"Please confirm if the simulation will be performed under the latest operating conditions?",viewCurrentSim:"View the Operating Condition",execOriginSim:"Execute the Original",execNewSim:"Execute the New"},mgtDetail:{group:"Group",open:"Opening",close:"Closing",fast:"Express Train",slow:"Slow Train",success:"Successful",fail:"Failed",gmonth:"{space}month{status}",month:"{space}month{status}",week:"{space}week{status}",day:"{space}day{status}",hour:"{space}hour{status}",minute:"{space}minute{status}",second:"{space}second{status}",ac:"AC",dc:"DC",autoExecute:"AutoExec",manualExecute:"Manual Execution",filename:"File Name",msg_filename_null:"Please Enter Filename",edit:"Modify",cancelEidt:"Cancel Changes",saveEidt:"Save Changes",msg_edit_not_save:"Please save first, or cancel.",loadingText:"Loading",tablenNoDataText:"No Data",cancleText:"Cancel",confirmText:"Confirm",continueAdd:"Continue Add",confirmData:"Confirm Data",dataResult:"Data Result",backReselect:"Reselection",syncConfig:"Synchronous Configuration",autoResize:"Adaptive",updateTime:"Update Time",dataUpdateTime:"Data Update Time",conditionDetail:"Working Condition Details",simResultList:"The list of simulation results",runSimulation:"Run Simulation",runSimulationPromptText:"Whether to perform simulation on the currently operating condition?",simulationTaskSubmitted:"The simulation task has been submitted！",simulationTaskSubmittedPromptText:"After simulation, The Results can be viewed in {result}",scheduledTask:"Add Scheduled Task",configSimParameters:"Configure SIM Parameters",viewCondition:"View Condition Info",search:"Search",search_placeholder:"Please Enter Equipment/Cable Name To Support Fuzzy Search",search_placeholder2:"Please Enter Station To Support Fuzzy Search",syncConfigData:"Sync Config Data",syncPromptText:"The system will synchronize your modified parameters with the simulation configuration data. Are you sure to continue?",undoAllChanges:"Undo All Edits",undoPromptText:"The system will undo your modified parameters and revert to the previous state. Are you sure to continue?",downloadConfigTemplate:"Download Config Template",importConfigFile:"Import Config File",exportConfigFile:"Export Config File",viewDetail:"View Detail",pleaseSelect:"Please Select",customDataStartTime:"Start Time",customDataEndTime:"End Time",checkLoad:"Verify",viewDataResult:"View Data",startTime:"Start Time",endTime:"End Time",endTimeLtStartTime:"End time shall not be less than the start time.",historyDataVerifySucc:"The historical data is valid.",historyDataVerifyFail:"The historical data is invalid.",recalculate:"Recalculate",saveSucc:"Save Successfully",saveFail:"Save Failed",number:"Number",condition:{id:"ID",importLoadPredictParameters:"Import Load Forecast Parameters",loadHistoryDataParameters:"Load Historical Parameters",selectForecastResult:"Select Load Forecast Results",task:"Task",forecastStartTime:"Prediction Start Time",forecastEndTime:"Prediction End Time",forecastDuration:"Prediction Duration",granularity:"Granularity",baseInfo:"Operating Condition Info",name:"Name",des:"Description",creator:"Creator",createTime:"Creation Time",electType:"Type",associatedSimTask:"Associate Scheduled Task",associatedSimRecord:"Historical SIM Results"},template:{syncSucc:"The synchronization was successed.",syncError:"The synchronization was failed."},result:{exportElecPicture:"Export Circuit Diagram",exportSimResult:"Export Result Data",exportSimAbnormalResult:"Export Abnormal Data Only",exportConfigFile:"Export Config File",showAlermMsg:"Display Alarm Msgs",showEditedParams:"Display Edited Parameters",allParams:"All Parameters",editedParams:" Edited Parameters",allData:"All Data",isAbnormal:"Is It Abnormal",abnormalData:"Abn. Data",simResult:"Simulation Result",presetThreshold:"Default Thresholds",viewConfigParams:"View Config Parameters",viewSimResult:"View SIM Results",viewSimInfo:"View Result Info",editElecThreshold:"Modify Thresholds",simBaseInfo:"SIM Result Info",relatedCondition:"Associated Condition",conditionDes:"Description",conditionType:"Type",triggerMode:"Trigger",executor:"Executor",taskStartTime:"Task StartTime",taskEndTime:"Task EndTime",simDuration:"Duration of SIM",deviceCategory:"Category",resultLabel:"Result Label",overload:"Overload",lowLod:"Low Load",deviceCable:"Equipment/Cable",device:"Equipment",cable:"Cable",result:"Result",currentLoadThreshold:"Threshold",exceedRatio:"Exceed Ratio",reason:"Fault Reason",deviceCapacity:"Capacity",from:"Fault From",capacity:"Capacity",currentCapacity:"Icable",crossSection:"Cross Section",rtGroup:"RT Enable Number"}},simTool:{edit_prop:"Edit",delete_prop:"Delete",common:"Common",circuit:"Circuit",category:"Category",station:"Station",url:"Line",status:"Status",associate:"Associate",please_input:"Please Input",on:"Enable",off:"Disable",canvasConfig:"Canvas Configuration",canvas:"Canvas",size:"Size",color:"Color",scale:"Scale",bg_color:"Background Color",anchor:"{status} Anchor",grid:"{status} Grid",rotate:"{status} Rotation",lineStyle:"Line Style",line:"Line",polyline:"Polyline",curvedline:"Curvedline",watermark:"Watermark",content:"Content",degree:"Degree",attribute:"Attribute",shape:"Shape",width:"Width",height:"Height",zIndex:"Tier",data:"Data",device:"Device No",name:"Name",addData:"Add Data",attribute_name:"Attribute Name",attribute_value:"Attribute Value",attribute_alias:"Attribute Alias",isNotNull:"{name} Cannot be empty",isNotRepeat:"{name} Already exists, cannot be added repeatedly",appearance:"Appearance",fill:"Fill",opacity:"Opacity",stroke:"Stroke",stroke_style:"Stroke Style",stroke_width:"Stroke Width",stroke_opacity:"Stroke Opacity",radius:"Radius",arrow:"{status} Arrow",animation:"{status} Animation",in_line:"Inlet",out_line:"Outlet",let_auto:"Default",has_line:"Has Line",text:"Text",text_placeholder:"Please Enter The Text",font_size:"Font Size",font_family:"Font Family",line_height:"Line Height"},topNavigation:{accident:"ACCIDENT",abnormal:"ABNORMAL",notification:"NOTIFICATION",pendingStatus:"Pending",processedStatus:"Processed",viewDetails:"Find Out More"},overview:{title:"MTR Electrical System Diagram",ac_electric:"AC Line",dc_electric:"DC Line",input_placeholder:"Equipment Name",acknowledge:"Acknowledge",dealPromptly:"Details",point:"Equipment Location",name:"Name",fault_startTime:"Time",fault_duration:"Duration",equ_location:"Equipment Location",fault_description:"Fault Description",fault_Date:"Time",recovery_advice:"Recovery Advice",promptText1:"Not enough authority."},mount:{title:"Data Loading Time",description1:"",description2:"Hours Before The Start of Simulation Tasks Execution. (Range: 2-22)",cancel:"Cancel",saveChanges:"Save Changes",saveSucces:"Saved successfully.",prompt:"Please enter a positive integer between 2-22."}}},l=n(40184);const r=(0,i.hU)({legacy:!1,globalInjection:!0,locale:(0,l.HQ)()??"zh_HK",messages:{zh_CN:o,zh_HK:a,en_US:s}});var c=r},38587:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_canshu",use:"icon-icon_canshu-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_canshu">\n    <title>切片</title>\n    <g id="icon-icon_canshu_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_canshu_侧边菜单栏" transform="translate(-2248.000000, -272.000000)" fill="currentColor">\n            <g id="icon-icon_canshu_编组备份" transform="translate(2220.000000, 0.000000)">\n                <g id="icon-icon_canshu_侧边菜单栏" transform="translate(0.000000, 104.000000)">\n                    <g id="icon-icon_canshu_预警&amp;告警管理备份-5" transform="translate(16.000000, 156.000000)">\n                        <g id="icon-icon_canshu_Icon/参数设置_1" transform="translate(12.000000, 12.000000)">\n                            <path d="M6.5,14 C7.43191279,14 8.21495822,14.6373769 8.43698631,15.4999808 L19,15.5 L19,16.5 L8.43672788,16.5010222 C8.21435119,17.3631171 7.43155158,18 6.5,18 C5.56844842,18 4.78564881,17.3631171 4.56327212,16.5010222 L1,16.5 L1,15.5 L4.56301369,15.4999808 C4.78504178,14.6373769 5.56808721,14 6.5,14 Z M6.5,15 C5.94771525,15 5.5,15.4477153 5.5,16 C5.5,16.5522847 5.94771525,17 6.5,17 C7.05228475,17 7.5,16.5522847 7.5,16 C7.5,15.4477153 7.05228475,15 6.5,15 Z M14.5,8 C15.4319128,8 16.2149582,8.63737692 16.4369863,9.49998077 L19,9.5 L19,10.5 L16.43643,10.5002609 C16.3478309,10.8431744 16.1689198,11.1595073 15.9142136,11.4142136 C15.5391408,11.7892863 15.030433,12 14.5,12 C13.5684484,12 12.7856488,11.3631171 12.5632721,10.5010222 L1,10.5 L1,9.5 L12.5630137,9.49998077 C12.7850418,8.63737692 13.5680872,8 14.5,8 Z M14.5,9 C13.9477153,9 13.5,9.44771525 13.5,10 C13.5,10.5522847 13.9477153,11 14.5,11 C15.0522847,11 15.5,10.5522847 15.5,10 C15.5,9.44771525 15.0522847,9 14.5,9 Z M5.5,1.5 C6.43191279,1.5 7.21495822,2.13737692 7.43698631,2.99998077 L19,3 L19,4 L7.43643005,4.00026095 C7.34783087,4.34317435 7.16891983,4.65950729 6.91421356,4.91421356 C6.53914081,5.28928632 6.03043298,5.5 5.5,5.5 C4.56844842,5.5 3.78564881,4.86311708 3.56327212,4.00102216 L1,4 L1,3 L3.56301369,2.99998077 C3.78504178,2.13737692 4.56808721,1.5 5.5,1.5 Z M5.5,2.5 C4.94771525,2.5 4.5,2.94771525 4.5,3.5 C4.5,4.05228475 4.94771525,4.5 5.5,4.5 C6.05228475,4.5 6.5,4.05228475 6.5,3.5 C6.5,2.94771525 6.05228475,2.5 5.5,2.5 Z" id="icon-icon_canshu_形状" />\n                        </g>\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},40184:function(e,t,n){"use strict";n.d(t,{HQ:function(){return c},Qq:function(){return a},ZP:function(){return u},e4:function(){return l},ep:function(){return s},iT:function(){return d}});var i=n(32052);const o="authorization";function a(){return i.A.get(o)}function s(e){return i.A.set(o,e)}function l(){i.A.remove(o)}const r="lang";function c(){return i.A.get(r)}function u(e){return i.A.set(r,e)}function d(){const e=window.location.href,t=e.substring(e.indexOf("?")+1),n=t.split("&"),i={};for(let o=0;o<n.length;o++){const e=n[o].split("=");i[e[0]]=e[1]}return i}},40641:function(e,t,n){"use strict";var i=n(34686),o=n(42357),a=n(57993),s=n(40184),l=n(54250),r=n(37992);const{t:c}=r.A.global,u=encodeURIComponent(window.location.href),d=`/mtr/login?callBack=${u}`,m=["/virtual/base/data/heartbeat"],g=a.A.create({baseURL:"/mtr-sim/service/",timeout:15e3,withCredentials:!0});g.interceptors.request.use(e=>{let t=(0,s.Qq)();const n=(0,s.iT)();return!t&&n.hasOwnProperty("token")&&(t=n.token??""),t&&(e.headers["authorization"]=t),e.headers["lang"]=(0,s.HQ)()??"zh_HK",e},e=>(console.log("....error"),Promise.reject(e))),g.interceptors.response.use(e=>{const t=e.data;if(e.config.url&&m.includes(e.config.url))return{code:200,data:t};const n=e.headers["download-filename"]??"";if((0,l.qf)(t))return{code:200,data:t,isBlob:!0,fileName:n};if((0,l.mw)(t))return{code:200,data:t,isArrayBuffer:!0,fileName:n};if(200!=t.code){let e=JSON.parse(o.A.local.get("user_tips_not_show")??"false");e||(0,i.nk)({message:t.msg||t.message,type:"error",duration:2e3}),1000007!=t.code&&1000008!=t.code&&1000012!=t.code||(o.A.local.remove("user_info"),o.A.local.remove("authorization"),(0,s.e4)(),location.href=d)}return t},e=>{const{status:t,statusText:n="",data:o}=e?.response??e;return e.response.config.url&&m.includes(e.response.config.url)?Promise.resolve({code:200}):("ECONNABORTED"===e.code?(0,i.nk)({message:c("golbal.requestTimeout"),type:"error",offset:80,duration:2e3}):(0,i.nk)({message:o?.msg??e?.message??n,type:"error",duration:2e3}),Promise.resolve({code:t,data:n}))}),t.A=g},40763:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_edit",use:"icon-icon_edit-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_edit">\n    <title>切片</title>\n    <g id="icon-icon_edit_页面" stroke="none" stroke-width="1" fill="current" fill-rule="evenodd">\n        <g id="icon-icon_edit_仿真与故障恢复建议_仿真结果列表_查看仿真结果（Cable）1" transform="translate(-1758.000000, -585.000000)" fill="current">\n            <g id="icon-icon_edit_button/primary_icon备份-3" transform="translate(1746.000000, 576.000000)">\n                <g id="icon-icon_edit_Icon/add" transform="translate(12.000000, 9.000000)">\n                    <path d="M0.875,11.375 L13.125,11.375 L13.125,12.25 L0.875,12.25 L0.875,11.375 Z M11.1125,3.9375 C11.4625,3.5875 11.4625,3.0625 11.1125,2.7125 L9.5375,1.1375 C9.1875,0.7875 8.6625,0.7875 8.3125,1.1375 L1.75,7.7 L1.75,10.5 L4.55,10.5 L11.1125,3.9375 Z M8.925,1.75 L10.5,3.325 L9.1875,4.6375 L7.6125,3.0625 L8.925,1.75 Z M2.625,9.625 L2.625,8.05 L7,3.675 L8.575,5.25 L4.2,9.625 L2.625,9.625 Z" id="icon-icon_edit_Fill" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},42357:function(e,t){"use strict";const n={local:{has:e=>Object.prototype.hasOwnProperty.call(localStorage,`${e}`),get:e=>localStorage.getItem(`${e}`),set:(e,t)=>{localStorage.setItem(`${e}`,t)},remove:e=>{localStorage.removeItem(`${e}`)},clear:()=>{localStorage.clear()}},session:{has:e=>Object.prototype.hasOwnProperty.call(sessionStorage,`${e}`),get:e=>sessionStorage.getItem(`${e}`),set:(e,t)=>{sessionStorage.setItem(`${e}`,t)},remove:e=>{sessionStorage.removeItem(`${e}`)},clear:()=>{sessionStorage.clear()}}};t.A=n},42550:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_warning-filled",use:"icon-icon_warning-filled-usage",viewBox:"0 0 24 24",content:'<symbol viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_warning-filled">\n    <title>切片</title>\n    <g id="icon-icon_warning-filled_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_warning-filled_仿真与故障恢复建议_角色管理_批量解绑用户" transform="translate(-784.000000, -468.000000)" fill="currentColor">\n            <g id="icon-icon_warning-filled_编组" transform="translate(760.000000, 444.000000)">\n                <g id="icon-icon_warning-filled_Icon/warning-filled" transform="translate(24.000000, 24.000000)">\n                    <path d="M12,1.5 C17.7989899,1.5 22.5,6.20101013 22.5,12 C22.5,17.7989899 17.7989899,22.5 12,22.5 C6.20101013,22.5 1.5,17.7989899 1.5,12 C1.5,6.20101013 6.20101013,1.5 12,1.5 Z M12,15.375 C11.3786797,15.375 10.875,15.8786797 10.875,16.5 C10.875,17.1213203 11.3786797,17.625 12,17.625 C12.6213203,17.625 13.125,17.1213203 13.125,16.5 C13.125,15.8786797 12.6213203,15.375 12,15.375 Z M12.9375,6.75 L11.0625,6.75 L11.0625,13.5 L12.9375,13.5 L12.9375,6.75 Z" id="icon-icon_warning-filled_形状结合" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},46177:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_user3",use:"icon-icon_user3-usage",viewBox:"0 0 16 16",content:'<symbol viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_user3">\n    <title>切片</title>\n    <g id="icon-icon_user3_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill-opacity="0.6">\n        <g id="icon-icon_user3_画板" transform="translate(-1812.000000, -132.000000)" fill="currentColor">\n            <g id="icon-icon_user3_导航8备份-2" transform="translate(-1.000000, 100.000000)">\n                <g id="icon-icon_user3_编组-18" transform="translate(1693.000000, 24.000000)">\n                    <g id="icon-icon_user3_Icon/User" transform="translate(120.000000, 8.000000)">\n                        <rect id="icon-icon_user3_矩形" fill-rule="nonzero" opacity="0" x="0" y="0" width="16" height="16" />\n                        <path d="M8,1 C10.209139,1 12,2.790861 12,5 C12,7.209139 10.209139,9 8,9 C5.790861,9 4,7.209139 4,5 C4,2.790861 5.790861,1 8,1 Z M8,2 C6.34314575,2 5,3.34314575 5,5 C5,6.65685425 6.34314575,8 8,8 C9.65685425,8 11,6.65685425 11,5 C11,3.34314575 9.65685425,2 8,2 Z" id="icon-icon_user3_椭圆形" fill-rule="nonzero" />\n                        <path d="M10,10 C12.209139,10 14,11.790861 14,14 L14,15 L13,15 L13,14.3333333 C13,12.4923842 11.5076158,11 9.66666667,11 L6.33333333,11 C4.55374914,11 3.09987957,12.3945501 3.00493225,14.1504427 L3,14.3333333 L3,15 L2,15 L2,14 C2,11.790861 3.790861,10 6,10 L10,10 Z" id="icon-icon_user3_形状结合" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},46892:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_show",use:"icon-icon_show-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_show">\n    <title>切片</title>\n    <defs>\n        <polygon id="icon-icon_show_path-1" points="0 0 18.75 0 18.75 17.5 0 17.5" />\n    </defs>\n    <g id="icon-icon_show_页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_show_登录页" transform="translate(-1716.000000, -553.000000)">\n            <g id="icon-icon_show_Icon/show" transform="translate(1716.000000, 553.000000)">\n                <g id="icon-icon_show_编组" transform="translate(0.625000, 1.250000)">\n                    <g>\n                        <mask id="icon-icon_show_mask-2" fill="white">\n                            <use xlink:href="#icon-icon_show_path-1" />\n                        </mask>\n                        <g id="icon-icon_show_Clip-2"></g>\n                        <path d="M0,8.75 C1.33270264,11.25 4.68468694,15.625 9.37312462,15.625 C14.0615623,15.625 17.5132751,11.25 18.75,8.75 C17.4888611,6.25 14.0615623,1.87696401 9.37312462,1.875 C4.68468694,1.87696401 1.28723145,6.25 0,8.75 Z M9.37361324,3.125 L9.37268874,3.125 C13.3693179,3.12593734 16.211242,6.68690635 17.5,8.70313281 C16.0207942,10.7099858 13.1659271,14.375 9.37361324,14.375 C5.58129943,14.375 2.72828128,10.7081111 1.25,8.70313281 C2.53783353,6.68784369 5.38068214,3.12593734 9.37361324,3.125" id="icon-icon_show_Fill-1" fill="#A5A8AD" mask="url(#icon-icon_show_mask-2)" />\n                    </g>\n                    <path d="M9.375,5 C7.3040625,5 5.625,6.6790625 5.625,8.75 C5.625,10.8209375 7.3040625,12.5 9.375,12.5 C11.446875,12.5 13.125,10.8209375 13.125,8.75 C13.125,6.6790625 11.446875,5 9.375,5 M9.375,6.25 C10.7533333,6.25 11.875,7.37166667 11.875,8.75 C11.875,10.1283333 10.7533333,11.25 9.375,11.25 C7.9975,11.25 6.875,10.1283333 6.875,8.75 C6.875,7.37166667 7.9975,6.25 9.375,6.25" id="icon-icon_show_Fill-3" fill="#A5A8AD" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},48833:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_export",use:"icon-icon_export-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_export">\n    <title>切片</title>\n    <g id="icon-icon_export_页面" stroke-width="1" fill-rule="evenodd">\n        <g id="icon-icon_export_仿真与故障恢复建议_工况列表_配置参数仿真（InfeedBreaker）" transform="translate(-1758.000000, -620.000000)" fill-rule="nonzero">\n            <g id="icon-icon_export_编组-12" transform="translate(288.000000, 548.000000)">\n                <g id="icon-icon_export_button/primary_icon备份-3" transform="translate(1458.000000, 63.000000)">\n                    <g id="icon-icon_export_Icon/export" transform="translate(12.000000, 9.000000)">\n                        <polygon id="icon-icon_export_路径" points="10.7866171 8.58448473 13.125 10.8928884 10.7866171 13.201292 10.1719032 12.5785948 11.391625 11.3748597 7 11.375 7 10.5 11.48175 10.4998597 10.1719032 9.20718194" />\n                        <path d="M11.375,7.875 L10.5,7.875 L10.5,5.25 L7,5.25 L7,1.75 L1.75,1.75 L1.75,12.25 L7,12.25 L7,13.125 L0.875,13.125 L0.875,0.875 L7.875,0.875 L11.375,4.375 L11.375,7.875 Z M7.875,2.113125 L7.875,4.375 L10.136875,4.375 L7.875,2.113125 Z" id="icon-icon_export_形状结合" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},50458:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_camera",use:"icon-icon_camera-usage",viewBox:"0 0 48 48",content:'<symbol viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg" id="icon-icon_camera">\r\n  <path d="M15 12L18 6H30L33 12H15Z" fill="#333" stroke="#333" stroke-width="4" stroke-linejoin="round" />\r\n  <rect x="4" y="12" width="40" height="30" rx="3" fill="#333" stroke="#333" stroke-width="4" stroke-linejoin="round" />\r\n  <path d="M24 35C28.4183 35 32 31.4183 32 27C32 22.5817 28.4183 19 24 19C19.5817 19 16 22.5817 16 27C16 31.4183 19.5817 35 24 35Z" fill="#FFF" stroke="#FFF" stroke-width="4" stroke-linejoin="round" />\r\n</symbol>'});s().add(l);t["default"]=l},50896:function(e,t,n){"use strict";n.d(t,{r:function(){return i}});const i=[{path:"/simulationMGT",component:()=>Promise.resolve().then(n.bind(n,70079)),name:"simulationMGT",meta:{code:"M4",title:"仿真与故障恢复建议"}},{path:"/simulationMGT/SIM",name:"SIM",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_gzhf",code:"M401",title:"仿真与故障恢复建议"}},{path:"/simulationMGT/operatingCondition",name:"operatingCondition",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_gklb",code:"M40101",title:"工况列表"}},{path:"/simulationMGT/scheduledTask",name:"scheduledTask",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_dsrw",code:"M40102",title:"定时仿真任务列表"}},{path:"/simulationMGT/result",name:"result",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_jglb",code:"M40103",title:"仿真结果列表"}},{path:"/simulationMGT/SIMSetting",name:"SIMSetting",component:()=>n.e(3754).then(n.bind(n,13754)),meta:{icon:"icon_setting",code:"M40106",title:"仿真设置",menu:"simulationMGT"}},{path:"/simulationMGT/SIMSetting/electricalThreshold",name:"electricalThreshold",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_dqyz",code:"M40104",title:"仿真电气阈值"}},{path:"/simulationMGT/SIMSetting/templateMGT",name:"templateMGT",component:()=>n.e(2058).then(n.bind(n,32058)),meta:{icon:"icon_mbgl",code:"M40105",title:"仿真模板管理"}},{path:"/simulationMGT/SIMSetting/mount",name:"mount",component:()=>n.e(1647).then(n.bind(n,81647)),meta:{icon:"icon_mount",code:"M40107",title:"仿真挂载设置"}}]},52323:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_download",use:"icon-icon_download-usage",viewBox:"0 0 16 16",content:'<symbol viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_download">\n    <title>切片</title>\n    <g id="icon-icon_download_页面" stroke-width="1" fill-rule="evenodd">\n        <g id="icon-icon_download_仿真与故障恢复建议_工况列表_配置参数仿真（InfeedBreaker）" transform="translate(-1488.000000, -620.000000)" fill-rule="nonzero">\n            <g id="icon-icon_download_编组-12" transform="translate(288.000000, 548.000000)">\n                <g id="icon-icon_download_button/primary_icon备份-2" transform="translate(1188.000000, 63.000000)">\n                    <g id="icon-icon_download_Icon/download" transform="translate(12.000000, 9.000000)">\n                        <path d="M8.35355339,4.14644661 L13.3535534,9.14644661 L12.6464466,9.85355339 L8.49944661,5.706 L8.5,15 L7.5,15 L7.49944661,5.706 L3.35355339,9.85355339 L2.64644661,9.14644661 L7.64644661,4.14644661 C7.84170876,3.95118446 8.15829124,3.95118446 8.35355339,4.14644661 Z M3,1 L13,1 L13,2 L3,2 L3,1 Z" id="icon-icon_download_形状结合" transform="translate(8.000000, 8.000000) scale(1, -1) translate(-8.000000, -8.000000) " />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},53583:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_yonghu",use:"icon-icon_yonghu-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_yonghu">\n    <title>切片</title>\n    <g id="icon-icon_yonghu_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_yonghu_侧边菜单栏" transform="translate(-2248.000000, -116.000000)" fill="currentColor">\n            <g id="icon-icon_yonghu_编组备份" transform="translate(2220.000000, 0.000000)">\n                <g id="icon-icon_yonghu_侧边菜单栏" transform="translate(0.000000, 104.000000)">\n                    <g id="icon-icon_yonghu_预警&amp;告警管理备份-2" transform="translate(16.000000, 0.000000)">\n                        <g id="icon-icon_yonghu_Icon/用户管理_1" transform="translate(12.000000, 12.000000)">\n                            <path d="M7,3 C8.93299662,3 10.5,4.56700338 10.5,6.5 C10.5,8.43299662 8.93299662,10 7,10 C5.06700338,10 3.5,8.43299662 3.5,6.5 C3.5,4.56700338 5.06700338,3 7,3 L7,3 Z M7,2 C4.51471863,2 2.5,4.01471863 2.5,6.5 C2.5,8.98528137 4.51471863,11 7,11 C9.48528137,11 11.5,8.98528137 11.5,6.5 C11.5,4.01471863 9.48528137,2 7,2 Z M13,18 L12,18 L12,15.5 C12,14.1192881 10.8807119,13 9.5,13 L4.5,13 C3.11928813,13 2,14.1192881 2,15.5 L2,18 L1,18 L1,15.5 C1,13.5670034 2.56700338,12 4.5,12 L9.5,12 C11.4329966,12 13,13.5670034 13,15.5 L13,18 Z M14,3 L19,3 L19,4 L14,4 L14,3 Z M14,6.5 L19,6.5 L19,7.5 L14,7.5 L14,6.5 Z M14,10 L17.5,10 L17.5,11 L14,11 L14,10 Z" id="icon-icon_yonghu_Fill" />\n                        </g>\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},54250:function(e,t,n){"use strict";n.d(t,{AY:function(){return c},Gv:function(){return l},Kg:function(){return o},RI:function(){return u},Tn:function(){return s},Ud:function(){return h},am:function(){return f},cy:function(){return a},fq:function(){return d},lN:function(){return m},mw:function(){return p},qf:function(){return g},rI:function(){return w}});var i=n(55787);function o(e){const t=Object.prototype.toString.call(e);return"[object String]"===t}function a(e){const t=Object.prototype.toString.call(e);return"[object Array]"===t}function s(e){const t=Object.prototype.toString.call(e);return"[object Function]"===t}function l(e){const t=Object.prototype.toString.call(e);return"[object Object]"===t}function r(e){const t=Object.prototype.toString.call(e);return"[object Null]"===t}function c(e){return!!e&&/^(https?:)/.test(e)}function u(e){return!!r(e)||!!l(e)&&0===Object.keys(e).length}function d(e,t){return!!l(e)&&Object.prototype.hasOwnProperty.call(e,t)}const m=(e,t)=>!!l(e)&&Object.prototype.hasOwnProperty.call(e,t),g=e=>{const t=Object.prototype.toString.call(e);return"[object Blob]"===t},p=e=>{const t=Object.prototype.toString.call(e);return"[object ArrayBuffer]"===t},f=(e,t)=>(e=h(e),t=h(t),o(e)||(e=JSON.stringify(e)),o(t)||(t=JSON.stringify(t)),e===t),h=e=>(0,i.g8)(e)?(e=(0,i.ux)(e),h(e)):(0,i.i9)(e)?(e=(0,i.R1)(e),h(e)):e;function w(e){let t=null;if(o(e))try{t=JSON.parse(e)}catch(n){t=null}else l(e)&&(t=e);return t}},56295:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-lf_scale_minus",use:"icon-lf_scale_minus-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-lf_scale_minus">\n    <title>切片</title>\n    <g id="icon-lf_scale_minus_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-lf_scale_minus_仿真与故障恢复建议_工况列表_查看详情备份" transform="translate(-1692.000000, -1007.000000)">\n            <g id="icon-lf_scale_minus_编组" transform="translate(1592.000000, 998.000000)">\n                <g id="icon-lf_scale_minus_Icon/reduce" transform="translate(100.000000, 9.000000)">\n                    <rect id="icon-lf_scale_minus_Rectangle" x="0.875" y="0.875" width="12.25" height="12.25" />\n                    <path d="M2.23837209,7.4375 C1.96841085,7.4375 1.75,7.27278068 1.75,7 C1.75,6.72721932 1.96841085,6.5625 2.23837209,6.5625 L11.7616279,6.5625 C12.0315891,6.5625 12.25,6.72721932 12.25,7 C12.25,7.27278068 12.0315891,7.4375 11.7616279,7.4375 L2.23837209,7.4375 Z" id="icon-lf_scale_minus_Shape" fill="#525866" transform="translate(7.000000, 7.000000) scale(1, -1) translate(-7.000000, -7.000000) " />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},56499:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_link",use:"icon-icon_link-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" id="icon-icon_link"><path d="M574 665.4c-3.1-3.1-8.2-3.1-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8c-3.1-3.1-8.2-3.1-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zM832.6 191.4c-84.6-84.6-221.5-84.6-306 0L410.3 307.6c-3.1 3.1-3.1 8.2 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6c-3.1 3.1-3.1 8.2 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1z" p-id="8462" /><path d="M610.1 372.3c-3.1-3.1-8.2-3.1-11.3 0L372.3 598.7c-3.1 3.1-3.1 8.2 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z" p-id="8463" /></symbol>'});s().add(l);t["default"]=l},56771:function(e,t,n){"use strict";var i=n(34686),o=n(42357),a=n(57993),s=n(40184),l=n(37992);const{t:r}=l.A.global,c=encodeURIComponent(window.location.href),u=`/mtr/login?callBack=${c}`,d=a.A.create({baseURL:"/mtr/service",timeout:15e3,withCredentials:!0});d.interceptors.request.use(e=>{let t=(0,s.Qq)();const n=(0,s.iT)();return!t&&n.hasOwnProperty("token")&&(t=n.token??""),t&&(e.headers["authorization"]=t),e.headers["lang"]=(0,s.HQ)()??"zh_HK",e},e=>{Promise.reject(e)}),d.interceptors.response.use(e=>{const t=e.data;if(200!=t.code){let e=JSON.parse(o.A.local.get("user_tips_not_show")??"false");e||(0,i.nk)({message:t.msg||t.message,type:"error",duration:2e3}),1000007!=t.code&&1000008!=t.code&&1000012!=t.code||(o.A.local.remove("user_info"),o.A.local.remove("authorization"),(0,s.e4)(),location.href=u)}return t},e=>{const{status:t,statusText:n="",data:o}=e?.response??e;return"ECONNABORTED"===e.code?(0,i.nk)({message:r("golbal.requestTimeout"),type:"error",offset:80,duration:2e3}):(0,i.nk)({message:o?.msg??e?.message??n,type:"error",duration:2e3}),Promise.reject(e)}),t.A=d},57250:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_geren",use:"icon-icon_geren-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_geren">\n    <title>icon_个人设置_1</title>\n    <g id="icon-icon_geren_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_geren_侧边菜单栏" transform="translate(-2248.000000, -220.000000)">\n            <g id="icon-icon_geren_编组备份" transform="translate(2220.000000, 0.000000)">\n                <g id="icon-icon_geren_侧边菜单栏" transform="translate(0.000000, 104.000000)">\n                    <g id="icon-icon_geren_预警&amp;告警管理备份-3" transform="translate(16.000000, 104.000000)">\n                        <g id="icon-icon_geren_icon_个人设置_1" transform="translate(12.000000, 12.000000)">\n                            <rect id="icon-icon_geren_矩形备份-55" x="0" y="0" width="20" height="20" />\n                            <path d="M10.5,2 C12.9852814,2 15,4.01471863 15,6.5 C15,8.91427333 13.0987635,10.8844891 10.7118357,10.995102 L10.5,11 C6.43908272,11 3.13198248,14.2274744 3.00385357,18.2572689 L3,18.5 L3,19 L2,19 L2,18.5 C2,14.6240939 4.59419933,11.3540956 8.14098286,10.3316201 C6.85649192,9.54081353 6,8.12042897 6,6.5 C6,4.01471863 8.01471863,2 10.5,2 Z M17,12 L19,15.5 L17,19 L13,19 L11,15.5 L13,12 L17,12 Z M16.419,13 L13.58,13 L12.152,15.499 L13.581,18 L16.418,18 L17.847,15.499 L16.419,13 Z M15,14.5 C15.5522847,14.5 16,14.9477153 16,15.5 C16,16.0522847 15.5522847,16.5 15,16.5 C14.4477153,16.5 14,16.0522847 14,15.5 C14,14.9477153 14.4477153,14.5 15,14.5 Z M10.5,3 C8.56700338,3 7,4.56700338 7,6.5 C7,8.43299662 8.56700338,10 10.5,10 C12.4329966,10 14,8.43299662 14,6.5 C14,4.56700338 12.4329966,3 10.5,3 Z" id="icon-icon_geren_形状结合" fill="currentColor" fill-rule="nonzero" />\n                        </g>\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},58426:function(e,t,n){var i={"./af":2848,"./af.js":2848,"./ar":18332,"./ar-dz":11699,"./ar-dz.js":11699,"./ar-kw":92519,"./ar-kw.js":92519,"./ar-ly":47758,"./ar-ly.js":47758,"./ar-ma":51879,"./ar-ma.js":51879,"./ar-ps":83816,"./ar-ps.js":83816,"./ar-sa":26621,"./ar-sa.js":26621,"./ar-tn":40855,"./ar-tn.js":40855,"./ar.js":18332,"./az":85252,"./az.js":85252,"./be":49926,"./be.js":49926,"./bg":87932,"./bg.js":87932,"./bm":18734,"./bm.js":18734,"./bn":19947,"./bn-bd":17062,"./bn-bd.js":17062,"./bn.js":19947,"./bo":38500,"./bo.js":38500,"./br":62167,"./br.js":62167,"./bs":72448,"./bs.js":72448,"./ca":14507,"./ca.js":14507,"./cs":60669,"./cs.js":60669,"./cv":40338,"./cv.js":40338,"./cy":27795,"./cy.js":27795,"./da":31944,"./da.js":31944,"./de":99796,"./de-at":39e3,"./de-at.js":39e3,"./de-ch":70606,"./de-ch.js":70606,"./de.js":99796,"./dv":58733,"./dv.js":58733,"./el":98822,"./el.js":98822,"./en-au":50199,"./en-au.js":50199,"./en-ca":58669,"./en-ca.js":58669,"./en-gb":79312,"./en-gb.js":79312,"./en-ie":24367,"./en-ie.js":24367,"./en-il":96880,"./en-il.js":96880,"./en-in":17306,"./en-in.js":17306,"./en-nz":92329,"./en-nz.js":92329,"./en-sg":56255,"./en-sg.js":56255,"./eo":68995,"./eo.js":68995,"./es":93631,"./es-do":52933,"./es-do.js":52933,"./es-mx":20605,"./es-mx.js":20605,"./es-us":5300,"./es-us.js":5300,"./es.js":93631,"./et":71326,"./et.js":71326,"./eu":97597,"./eu.js":97597,"./fa":57046,"./fa.js":57046,"./fi":33822,"./fi.js":33822,"./fil":85016,"./fil.js":85016,"./fo":29352,"./fo.js":29352,"./fr":96803,"./fr-ca":47768,"./fr-ca.js":47768,"./fr-ch":95143,"./fr-ch.js":95143,"./fr.js":96803,"./fy":6830,"./fy.js":6830,"./ga":15911,"./ga.js":15911,"./gd":68412,"./gd.js":68412,"./gl":61508,"./gl.js":61508,"./gom-deva":20661,"./gom-deva.js":20661,"./gom-latn":4078,"./gom-latn.js":4078,"./gu":3763,"./gu.js":3763,"./he":38664,"./he.js":38664,"./hi":70956,"./hi.js":70956,"./hr":65453,"./hr.js":65453,"./hu":49720,"./hu.js":49720,"./hy-am":79759,"./hy-am.js":79759,"./id":49410,"./id.js":49410,"./is":40411,"./is.js":40411,"./it":18674,"./it-ch":87148,"./it-ch.js":87148,"./it.js":18674,"./ja":23698,"./ja.js":23698,"./jv":63179,"./jv.js":63179,"./ka":56227,"./ka.js":56227,"./kk":75917,"./kk.js":75917,"./km":69423,"./km.js":69423,"./kn":30466,"./kn.js":30466,"./ko":14705,"./ko.js":14705,"./ku":74999,"./ku-kmr":58312,"./ku-kmr.js":58312,"./ku.js":74999,"./ky":4571,"./ky.js":4571,"./lb":33369,"./lb.js":33369,"./lo":23934,"./lo.js":23934,"./lt":10211,"./lt.js":10211,"./lv":69701,"./lv.js":69701,"./me":22949,"./me.js":22949,"./mi":55569,"./mi.js":55569,"./mk":27695,"./mk.js":27695,"./ml":47310,"./ml.js":47310,"./mn":74916,"./mn.js":74916,"./mr":91968,"./mr.js":91968,"./ms":90743,"./ms-my":66006,"./ms-my.js":66006,"./ms.js":90743,"./mt":74230,"./mt.js":74230,"./my":76673,"./my.js":76673,"./nb":15851,"./nb.js":15851,"./ne":72418,"./ne.js":72418,"./nl":40137,"./nl-be":19563,"./nl-be.js":19563,"./nl.js":40137,"./nn":88807,"./nn.js":88807,"./oc-lnc":82623,"./oc-lnc.js":82623,"./pa-in":43466,"./pa-in.js":43466,"./pl":19807,"./pl.js":19807,"./pt":2151,"./pt-br":12812,"./pt-br.js":12812,"./pt.js":2151,"./ro":90484,"./ro.js":90484,"./ru":19910,"./ru.js":19910,"./sd":34088,"./sd.js":34088,"./se":45407,"./se.js":45407,"./si":55251,"./si.js":55251,"./sk":53845,"./sk.js":53845,"./sl":13696,"./sl.js":13696,"./sq":18667,"./sq.js":18667,"./sr":11566,"./sr-cyrl":50097,"./sr-cyrl.js":50097,"./sr.js":11566,"./ss":60877,"./ss.js":60877,"./sv":75234,"./sv.js":75234,"./sw":43121,"./sw.js":43121,"./ta":98392,"./ta.js":98392,"./te":82596,"./te.js":82596,"./tet":78306,"./tet.js":78306,"./tg":81166,"./tg.js":81166,"./th":20647,"./th.js":20647,"./tk":93626,"./tk.js":93626,"./tl-ph":62552,"./tl-ph.js":62552,"./tlh":39579,"./tlh.js":39579,"./tr":33825,"./tr.js":33825,"./tzl":90113,"./tzl.js":90113,"./tzm":99186,"./tzm-latn":68946,"./tzm-latn.js":68946,"./tzm.js":99186,"./ug-cn":14157,"./ug-cn.js":14157,"./uk":823,"./uk.js":823,"./ur":60888,"./ur.js":60888,"./uz":84112,"./uz-latn":66160,"./uz-latn.js":66160,"./uz.js":84112,"./vi":10638,"./vi.js":10638,"./x-pseudo":69438,"./x-pseudo.js":69438,"./yo":42231,"./yo.js":42231,"./zh-cn":71511,"./zh-cn.js":71511,"./zh-hk":58679,"./zh-hk.js":58679,"./zh-mo":33682,"./zh-mo.js":33682,"./zh-tw":25855,"./zh-tw.js":25855};function o(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}o.keys=function(){return Object.keys(i)},o.resolve=a,e.exports=o,o.id=58426},60747:function(e,t,n){"use strict";var i=n(5638),o=n(55787),a=n(50443),s=n(42357),l=n(17771),r=n(40184);const c=(0,i.nY)("user",()=>{let e=(0,o.KR)((0,r.Qq)()??""),t=(0,a.EW)(()=>JSON.parse(s.A.local.get("user_info")||"{}"));function n(){return new Promise(e=>{(0,l.pG)({}).then(t=>{s.A.local.set("user_info",JSON.stringify(t.data)),e(!0)})})}function i(t){e.value=t,(0,r.ep)(t),s.A.local.set("authorization",t)}function c(){(0,l.jd)({}).then(e=>{if(200==e.code){s.A.local.remove("user_info"),s.A.local.remove("authorization"),(0,r.e4)();const e=encodeURIComponent(window.location.href);window.location.href=`/mtr/login?callBack=${e}`}})}return{token:e,userInfo:t,getUserInfo:n,setToken:i,userLogout:c}});t.A=c},62916:function(e,t,n){"use strict";n.d(t,{Ag:function(){return m},CU:function(){return a},Q2:function(){return c},Xq:function(){return r},Xw:function(){return g},bs:function(){return l},kY:function(){return u},rS:function(){return d},tF:function(){return s}});var i=n(54250),o=n(1534);function a(e,t=250,n){n=n||{leading:!1};let i=null,o=!1;return n?.leading&&(o=n?.leading),function(...n){null===i&&(o?(e.apply(this,n),i=setTimeout(()=>{clearTimeout(i),i=null},t)):i=setTimeout(()=>{clearTimeout(i),i=null,e.apply(this,n)},t))}}function s(e,t=250){let n=null;return function(...i){null!==n&&clearTimeout(n),n=setTimeout(()=>{clearTimeout(n),n=null,e.apply(this,i)},t)}}function l(e){return(0,o.A)(e)}function r(e=250){return new Promise(t=>{const n=setTimeout(()=>{clearTimeout(n),t(!0)},e)})}const c=(e={},t="文件")=>{const n=JSON.stringify(e),i=new Blob([n],{type:"application/json;charset=utf-8"}),o=URL.createObjectURL(i);let a=document.createElement("a");a.style.display="none",a.download=t,a.href=o,a.click(),a=null,URL.revokeObjectURL(o)},u=(e,t="文件.xlsx")=>{if(!e)return;if(!(0,i.qf)(e))return;const n=e,o=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"}),a=URL.createObjectURL(o);let s=document.createElement("a");s.style.display="none",s.download=t,s.href=a,s.click(),s=null,URL.revokeObjectURL(a)},d=(e="",t)=>{if(!e)return;if((0,i.AY)(e))return void window.open(e);const n=location.origin+`/sim/service/${e}`;window.open(n)};function m(e){const t=Function;return new t(`return ${e}`)()}function g(e,t,n){if("number"!==typeof e&&!Array.isArray(e))return console.error("参数必须是`数组`或者`数字`");if("number"===typeof e&&(e=new Array(e)),0===e.length)return[];let{chunkSplitor:i,finished:o}=n??{};i&&"function"===typeof i||(i=e=>{requestIdleCallback(t=>{e(()=>t.timeRemaining()>0)})});const a=e.length;let s=0;function l(){s!==a?i&&i(n=>{while(n()&&s<a)for(let n=0;n<a;n++){const i=e[n];t(i,n),s++}l()}):o&&o()}l()}},64227:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_gklb",use:"icon-icon_gklb-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_gklb">\n    <title>icon_工况列表</title>\n    <g id="icon-icon_gklb_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_gklb_仿真与故障恢复建议_仿真模板管理" transform="translate(-60.000000, -168.000000)">\n            <g id="icon-icon_gklb_告警规则备份" transform="translate(16.000000, 156.000000)">\n                <g id="icon-icon_gklb_icon_工况列表" transform="translate(44.000000, 12.000000)">\n                    <rect id="icon-icon_gklb_矩形备份-55" x="0" y="0" width="20" height="20" />\n                    <path d="M12.5,13 L12.5,15.583 L15.084,13 L12.5,13 Z M15.5,3 L4.5,3 L4.5,16 L11.5,16 L11.5,12 L15.5,12 L15.5,3 Z M10.5,11 L10.5,12 L6.5,12 L6.5,11 L10.5,11 Z M11.5,8 L11.5,9 L6.5,9 L6.5,8 L11.5,8 Z M13.5,5 L13.5,6 L6.5,6 L6.5,5 L13.5,5 Z M4.5,17 C3.94771525,17 3.5,16.5522847 3.5,16 L3.5,3 C3.5,2.44771525 3.94771525,2 4.5,2 L15.5,2 C16.0522847,2 16.5,2.44771525 16.5,3 L16.5,13 L12.5,17 L4.5,17 Z" id="icon-icon_gklb_形状" fill-opacity="0.6" fill="currentColor" fill-rule="nonzero" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},66441:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_juese",use:"icon-icon_juese-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_juese">\n    <title>icon_组织管理_1</title>\n    <g id="icon-icon_juese_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_juese_侧边菜单栏" transform="translate(-2248.000000, -168.000000)">\n            <g id="icon-icon_juese_编组备份" transform="translate(2220.000000, 0.000000)">\n                <g id="icon-icon_juese_侧边菜单栏" transform="translate(0.000000, 104.000000)">\n                    <g id="icon-icon_juese_预警&amp;告警管理备份-4" transform="translate(16.000000, 52.000000)">\n                        <g id="icon-icon_juese_icon_组织管理_1" transform="translate(12.000000, 12.000000)">\n                            <rect id="icon-icon_juese_矩形备份-55" x="0" y="0" width="20" height="20" />\n                            <path d="M15.5,12.5 C15.7761424,12.5 16.0261424,12.6119288 16.2071068,12.7928932 C16.3579105,12.9436969 16.4607727,13.1424419 16.4908713,13.3645868 L16.5000002,13.5004854 L16.5012309,14.7681802 C16.9410263,15.0231486 17.274878,15.4408172 17.4202005,15.9388794 C17.3281068,15.6303465 17.1610892,15.3518883 16.9316855,15.1224847 C16.5906834,14.7814825 16.1076453,14.5464147 15.5207771,14.5 L15.5207771,14.5 L14.5,14.5 L14.3179592,14.5081733 C13.9628013,14.5402181 13.6344224,14.6651088 13.357282,14.8583856 C13.3602036,14.8562947 13.3629433,14.8543939 13.3656879,14.8524997 L13.5000571,14.767434 L13.5,13.5 C13.5,13.2238576 13.6119288,12.9738576 13.7928932,12.7928932 C13.9436969,12.6420895 14.1424419,12.5392273 14.3643059,12.5091288 L14.5,12.5 L15.5,12.5 Z" id="icon-icon_juese_形状结合" stroke="currentColor" fill="currentColor" />\n                            <path d="M10.5,2 C12.9852814,2 15,4.01471863 15,6.5 C15,8.91427333 13.0987635,10.8844891 10.7118357,10.995102 L10.5,11 C6.43908272,11 3.13198248,14.2274744 3.00385357,18.2572689 L3,18.5 L3,19 L2,19 L2,18.5 C2,14.6240939 4.59419933,11.3540956 8.14098286,10.3316201 C6.85649192,9.54081353 6,8.12042897 6,6.5 C6,4.01471863 8.01471863,2 10.5,2 Z M10.5,3 C8.56700338,3 7,4.56700338 7,6.5 C7,8.43299662 8.56700338,10 10.5,10 C12.4329966,10 14,8.43299662 14,6.5 C14,4.56700338 12.4329966,3 10.5,3 Z" id="icon-icon_juese_形状结合" fill="currentColor" fill-rule="nonzero" />\n                        </g>\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},68265:function(e,t,n){"use strict";n(71201),n(66040),n(48773);var i=n(5638),o=n(55787),a=n(37992),s=n(40184),l=n(17771),r=n(3255),c=n(50896);const u=(0,i.nY)("settings",()=>{let e=(0,o.KR)((0,s.HQ)()??"zh_HK");(0,s.ZP)(e.value);let t=(0,o.KR)(""),n=(0,o.KR)(0),i=(0,o.KR)([]),u=(0,o.KR)([]);const d=(0,r.A)();function m(n){const{t:i}=a.A.global;e.value=n,(0,s.ZP)(n),a.A.global.locale.value=n,window.document.title=i(`routes.${t.value}`)??i("golbal.projectName")}function g(e){t.value=e}function p(){return new Promise(e=>{(0,l.nD)({}).then(t=>{let i=t.data??[];f(i),n.value=1,d.setAllRoutes(i),e(!0)})})}function f(e){return e.forEach(e=>{i.value.push(e.code),4==e.type&&(u.value.push(e.code),e.hidden=!0),e.childList.length>0&&f(e.childList),e.path=e.pathUrl,c.r.forEach(t=>{t.meta.code==e.code&&(e.meta=t.meta,1!=e.type&&(e.path=t.path))})}),e}function h(e){return-1!==u.value.indexOf(e)}function w(e){return-1!==i.value.indexOf(e)}return{lang:e,menuName:t,isRouter:n,menuCodeData:i,btnCodeData:u,setLang:m,setMenu:g,generateRoutes:p,findCode:h,findMenuCode:w}});t.A=u},69986:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_gzhf",use:"icon-icon_gzhf-usage",viewBox:"0 0 20 20",content:'<symbol viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_gzhf">\n    <title>icon_故障恢复建议</title>\n    <g id="icon-icon_gzhf_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_gzhf_仿真与故障恢复建议_仿真模板管理" transform="translate(-28.000000, -116.000000)">\n            <g id="icon-icon_gzhf_预警&amp;告警管理备份-2" transform="translate(16.000000, 104.000000)">\n                <g id="icon-icon_gzhf_icon_故障恢复建议" transform="translate(12.000000, 12.000000)">\n                    <rect id="icon-icon_gzhf_矩形备份-55" x="0" y="0" width="20" height="20" />\n                    <path d="M14,9.5 C16.209139,9.5 18,11.290861 18,13.5 C18,15.709139 16.209139,17.5 14,17.5 C11.790861,17.5 10,15.709139 10,13.5 C10,11.290861 11.790861,9.5 14,9.5 Z M14,2 C14.5522847,2 15,2.44771525 15,3 L15.0011864,8.60025909 C14.6777087,8.5345146 14.342885,8.5 14,8.5 L14,3 L4,3 L4,16 L9.66966156,16.0012971 C9.88116163,16.3666647 10.1375754,16.7027855 10.4313517,17.0021084 L4,17 C3.44771525,17 3,16.5522847 3,16 L3,3 C3,2.44771525 3.44771525,2 4,2 L14,2 Z M14,10.3 C12.2326888,10.3 10.8,11.7326888 10.8,13.5 C10.8,15.2673112 12.2326888,16.7 14,16.7 C15.7673112,16.7 17.2,15.2673112 17.2,13.5 C17.2,11.7326888 15.7673112,10.3 14,10.3 Z M14,15 C14.2761424,15 14.5,15.2238576 14.5,15.5 C14.5,15.7761424 14.2761424,16 14,16 C13.7238576,16 13.5,15.7761424 13.5,15.5 C13.5,15.2238576 13.7238576,15 14,15 Z M14,11.5 C14.2540877,11.5 14.4600664,11.7059788 14.4600664,11.9600664 L14.4584773,11.9982729 L14.2707614,14.2508636 C14.2590264,14.3916835 14.141308,14.5 14,14.5 C13.858692,14.5 13.7409736,14.3916835 13.7292386,14.2508636 L13.5415227,11.9982729 C13.5204219,11.7450629 13.7085836,11.52269 13.9617936,11.5015892 C13.9745015,11.5005302 13.987248,11.5 14,11.5 Z M8,11 L8,12 L5,12 L5,11 L8,11 Z M9,8 L9,9 L5,9 L5,8 L9,8 Z M12,5 L12,6 L5,6 L5,5 L12,5 Z" id="icon-icon_gzhf_形状结合" fill="currentColor" fill-rule="nonzero" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},70079:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return N}});var i=n(50443),o=n(36334),a=n(55787),s=(n(66040),n(82144),n(10514)),l=n(68265),r=n(90262);function c(e){return/^(https?:|mailto:|tel:)/.test(e)}const u={key:1};var d=(0,i.pM)({__name:"Item",props:{icon:{type:String,default:""},title:{type:String,default:""}},setup(e){return(t,n)=>{const o=(0,i.g2)("svg-icon");return(0,i.uX)(),(0,i.CE)(i.FK,null,[e.icon?((0,i.uX)(),(0,i.Wv)(o,{key:0,name:e.icon,class:"menu-icon"},null,8,["name"])):(0,i.Q3)("",!0),e.title?((0,i.uX)(),(0,i.CE)("span",u,(0,s.v_)(e.title),1)):(0,i.Q3)("",!0)],64)}}}),m=n(48057);const g=(0,m.A)(d,[["__scopeId","data-v-5573bb61"]]);var p=g,f=(0,i.pM)({__name:"AppLink",props:{to:{type:String,required:!0}},setup(e){const t=e,n=(0,i.EW)(()=>c(t.to)),o=(0,i.EW)(()=>n.value?"a":"router-link");function a(e){return n.value?{href:e,target:"_blank",rel:"noopener"}:{to:e}}return(t,n)=>((0,i.uX)(),(0,i.Wv)((0,i.$y)(o.value),(0,s._B)((0,i.Ng)(a(e.to))),{default:(0,i.k6)(()=>[(0,i.RG)(t.$slots,"default")]),_:3},16))}});const h=f;var w=h;const v={key:0,class:"menu-wrapper"};var _=(0,i.pM)({__name:"SidebarItem",props:{item:{type:Object,required:!0},isNest:{type:Boolean,default:!1},basePath:{type:String,default:""}},setup(e){const t=e,n=(0,l.A)();let o=(0,i.EW)(()=>{let e="zh_CN"==n.lang?"cn":"zh_HK"==n.lang?"tc":"en";return e+"Name"}),u=(0,a.Kh)({});function d(e=[],t){const n=e.filter(e=>!e.hidden&&(u=e,!0));return 1===n.length||0===n.length&&(u={...t,path:"",noShowingchildList:!0},!0)}function m(e){return c(e)?e:c(t.basePath)?t.basePath:r.resolve(t.basePath,e)}return(n,l)=>{const r=(0,i.g2)("el-menu-item"),c=(0,i.g2)("sidebar-item",!0),g=(0,i.g2)("el-sub-menu");return t.item.hidden?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.CE)("div",v,[!d(t.item.childList,e.item)||(0,a.R1)(u).childList&&!(0,a.R1)(u).noShowingchildList||t.item?.alwaysShow?((0,i.uX)(),(0,i.Wv)(g,{key:1,ref:"subMenu",index:m(t.item.path)},{title:(0,i.k6)(()=>[t.item.meta?((0,i.uX)(),(0,i.Wv)(p,{key:0,icon:t.item.meta&&t.item.meta.icon,title:t.item[(0,a.R1)(o)]},null,8,["icon","title"])):(0,i.Q3)("",!0)]),default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(t.item.childList,e=>((0,i.uX)(),(0,i.Wv)(c,{key:e.path,"is-nest":!0,item:e,"base-path":m(e.path),class:"nest-menu"},null,8,["item","base-path"]))),128))]),_:1},8,["index"])):((0,i.uX)(),(0,i.CE)(i.FK,{key:0},[(0,a.R1)(u).meta?((0,i.uX)(),(0,i.Wv)(w,{key:0,to:m((0,a.R1)(u).path)},{default:(0,i.k6)(()=>[(0,i.bF)(r,{index:m((0,a.R1)(u).path),class:(0,s.C4)({"submenu-title-noDropdown":!e.isNest})},{default:(0,i.k6)(()=>[(0,i.bF)(p,{icon:(0,a.R1)(u).meta.icon||t.item.meta&&t.item.meta.icon,title:(0,a.R1)(u)[(0,a.R1)(o)]},null,8,["icon","title"])]),_:1},8,["index","class"])]),_:1},8,["to"])):(0,i.Q3)("",!0)],64))]))}}});const y=(0,m.A)(_,[["__scopeId","data-v-76f0bd14"]]);var C=y,b=n(82157),L=n(3255);const T={class:"sidebar-main"};var k=(0,i.pM)({__name:"index",setup(e){const t=(0,b.lq)(),n=(0,L.A)();let o=(0,i.EW)(()=>{const{meta:e,path:n}=t;return e.activeMenu?e.activeMenu:n}),s=(0,i.EW)(()=>n.routes.list||[]);return(e,t)=>{const n=(0,i.g2)("el-menu"),l=(0,i.g2)("el-scrollbar");return(0,i.uX)(),(0,i.CE)("div",T,[(0,i.bF)(l,{style:{height:"calc(100vh - 80px)"}},{default:(0,i.k6)(()=>[(0,i.bF)(n,{class:"menu-main",mode:"vertical","popper-offset":30,"default-active":(0,a.R1)(o)},{default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)((0,a.R1)(s),e=>((0,i.uX)(),(0,i.Wv)(C,{key:e.path,item:e,"base-path":e.path},null,8,["item","base-path"]))),128))]),_:1},8,["default-active"])]),_:1})])}}});const x=(0,m.A)(k,[["__scopeId","data-v-746c5d8e"]]);var S=x,D=n(38006),A=n(8018);const M={class:"app_main-main"},P={class:"smgt-container"},E={class:"smgt-content"};var R=(0,i.pM)({__name:"AppMain",setup(e){return(e,t)=>{const n=(0,i.g2)("router-view");return(0,i.uX)(),(0,i.CE)("div",M,[(0,i.bF)(D.eB,null,{default:(0,i.k6)(()=>[(0,i.Lk)("div",P,[(0,i.bF)(A.A),(0,i.Lk)("div",E,[(0,i.bF)(n,null,{default:(0,i.k6)(({Component:e})=>[((0,i.uX)(),(0,i.Wv)((0,i.$y)(e)))]),_:1})])])]),_:1})])}}});const I=(0,m.A)(R,[["__scopeId","data-v-44ef7887"]]);var j=I;const F={class:"app-main"};var z=(0,i.pM)({__name:"index",setup(e){return(e,t)=>((0,i.uX)(),(0,i.CE)("div",F,[(0,i.bF)(o.A),(0,i.bF)(S),(0,i.bF)(j)]))}});const B=z;var N=B},72922:function(e,t,n){var i={"./export.svg":33337,"./icon_camera.svg":50458,"./icon_canshu.svg":38587,"./icon_delete.svg":33686,"./icon_download.svg":52323,"./icon_dqyz.svg":27103,"./icon_dsrw.svg":19425,"./icon_edit.svg":40763,"./icon_export.svg":48833,"./icon_fullscreen.svg":95688,"./icon_geren.svg":57250,"./icon_gklb.svg":64227,"./icon_gzhf.svg":69986,"./icon_import.svg":87140,"./icon_info-filled.svg":16880,"./icon_jglb.svg":8540,"./icon_juese.svg":66441,"./icon_link.svg":56499,"./icon_lock.svg":82958,"./icon_mbgl.svg":33263,"./icon_mount.svg":95558,"./icon_msg.svg":7790,"./icon_password.svg":16744,"./icon_setting.svg":7641,"./icon_show.svg":46892,"./icon_success-filled.svg":98493,"./icon_user2.svg":10640,"./icon_user3.svg":46177,"./icon_warning-filled.svg":42550,"./icon_warning.svg":91437,"./icon_yonghu.svg":53583,"./lf_fullscreen.svg":2939,"./lf_scale_minus.svg":56295,"./lf_scale_plus.svg":19241,"./lf_select_arrow.svg":36322};function o(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}o.keys=function(){return Object.keys(i)},o.resolve=a,e.exports=o,o.id=72922},77543:function(e,t,n){"use strict";n.d(t,{A:function(){return b}});n(66040),n(5129);var i=n(82157),o=n(37992),a=n(60747),s=n(68265),l=n(3255),r=n(40184),c=n(70079);const u=[{path:"/simulationMGT",component:c["default"],name:"simulationMGT",meta:{code:"M4",title:"仿真与故障恢复建议",menu:"simulationMGT"},children:[{path:"operatingCondition",name:"operatingCondition",component:()=>Promise.all([n.e(981),n.e(8540)]).then(n.bind(n,68540)),meta:{icon:"icon_gklb",code:"M40101",title:"工况列表",menu:"simulationMGT"},children:[{path:"view",name:"operatingConditionViewDetail",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(5301),n.e(668)]).then(n.bind(n,88918)),meta:{icon:"icon_gklb",code:"M40101",title:"查看详情",menu:"simulationMGT"}},{path:"edit",name:"operatingConditionEditDetail",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(5301),n.e(4383)]).then(n.bind(n,97153)),meta:{icon:"icon_gklb",code:"M40101",title:"编辑详情",menu:"simulationMGT"}}]},{path:"scheduledTask",name:"scheduledTask",component:()=>Promise.all([n.e(981),n.e(5829)]).then(n.bind(n,35829)),meta:{icon:"icon_dsrw",code:"M40102",title:"定时仿真任务列表",menu:"simulationMGT"}},{path:"taskDetail",name:"taskDetail",component:()=>n.e(4707).then(n.bind(n,4707)),meta:{icon:"icon_dsrw",code:"M40102",title:"仿真任务配置",menu:"simulationMGT"}},{path:"result",name:"result",component:()=>Promise.all([n.e(981),n.e(3945)]).then(n.bind(n,43945)),meta:{icon:"icon_jglb",code:"M40103",title:"仿真结果列表",menu:"simulationMGT"},children:[{path:"view",name:"resultViewDetail",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(8499),n.e(7655)]).then(n.bind(n,17655)),meta:{icon:"icon_gklb",code:"M40103",title:"查看详情",menu:"simulationMGT"}}]},{path:"SIMSetting",name:"SIMSetting",component:()=>n.e(3754).then(n.bind(n,13754)),meta:{icon:"icon_setting",code:"M40106",title:"仿真设置",menu:"simulationMGT"},children:[{path:"electricalThreshold",name:"electricalThreshold",component:()=>Promise.all([n.e(279),n.e(4719)]).then(n.bind(n,74719)),meta:{icon:"icon_dqyz",code:"M40104",title:"仿真电气阈值",menu:"simulationMGT"}},{path:"templateMGT",name:"templateMGT",component:()=>Promise.all([n.e(981),n.e(8896)]).then(n.bind(n,68896)),meta:{icon:"icon_mbgl",code:"M40105",title:"仿真模板管理",menu:"simulationMGT"},children:[{path:"view",name:"templateMGTViewDetail",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(5301),n.e(2409)]).then(n.bind(n,50067)),meta:{icon:"icon_gklb",code:"M40105",title:"查看详情",menu:"simulationMGT"}},{path:"edit",name:"templateMGTEditDetail",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(5301),n.e(1877)]).then(n.bind(n,44559)),meta:{icon:"icon_gklb",code:"M40105",title:"编辑详情",menu:"simulationMGT"}}]},{path:"mount",name:"mount",component:()=>n.e(1647).then(n.bind(n,81647)),meta:{icon:"icon_mount",code:"M40107",title:"仿真挂载设置",menu:"simulationMGT"}}]}]}];var d=n(50443),m=n(38006),g=n(36334);const p={class:"app-main"};var f=(0,d.pM)({__name:"overview",setup(e){return(e,t)=>{const n=(0,d.g2)("router-view");return(0,d.uX)(),(0,d.CE)("div",p,[(0,d.bF)(g.A),(0,d.bF)(n,null,{default:(0,d.k6)(({Component:e})=>[(0,d.bF)(m.eB,null,{default:(0,d.k6)(()=>[((0,d.uX)(),(0,d.Wv)(d.PR,null,[((0,d.uX)(),(0,d.Wv)((0,d.$y)(e)))],1024))]),_:2},1024)]),_:1})])}}});const h=f;var w=h;const{t:v}=o.A.global,_=[{path:"/",redirect:"/simulationMGT"},...u,{path:"/overview",name:"overview",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(7966)]).then(n.bind(n,37966)),meta:{icon:"",code:"M40105",title:"综合概览",menu:"simulationMGT"}},{path:"/overview/energy",name:"overviewEnergy",component:()=>Promise.all([n.e(2305),n.e(279),n.e(9998),n.e(2213)]).then(n.bind(n,72213)),meta:{icon:"",code:"M40105",title:"综合概览",menu:"simulationMGT"}},{path:"/errorMGT",component:w,name:"errorMGT",children:[{path:"/404",name:"404",component:()=>n.e(9250).then(n.bind(n,74455))}]}],y=(0,i.aE)({history:(0,i.LA)("mtr-sim"),routes:_}),C=["/404","/overview","/overview/energy"];y.beforeEach(async(e,t,n)=>{const i=(0,s.A)(),o=(0,a.A)(),c=(0,l.A)();let u=e?.name??"";i.setMenu(u),window.document.title=v(`routes.${u}`)??v("golbal.projectName");let d=(0,r.Qq)();d||(d=e?.query?.token??"",d&&(0,r.ep)(d));const m="/mtr/login",g=encodeURIComponent(window.location.href);if(d){await o.getUserInfo(),1!=i.isRouter&&await i.generateRoutes();let t=e?.meta?.code??"";if(-1!==C.indexOf(e.path))n();else if(-1!==i.menuCodeData.indexOf(t))if("simulationMGT"==e.name){let e=c.allRoutes.list.find(e=>e.path?.includes("/simulationMGT"))?.childList[0]?.path;n(e)}else n();else window.location.href=`${m}?callBack=${g}`}else window.location.href=`${m}?callBack=${g}`});var b=y},82958:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_lock",use:"icon-icon_lock-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" id="icon-icon_lock"><path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240z m460 600H232V536h560v304z" p-id="8742" /><path d="M484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53c12.1-8.7 20-22.9 20-39 0-26.5-21.5-48-48-48s-48 21.5-48 48c0 16.1 7.9 30.3 20 39z" p-id="8743" /></symbol>'});s().add(l);t["default"]=l},87140:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_import",use:"icon-icon_import-usage",viewBox:"0 0 14 14",content:'<symbol viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_import">\n    <title>切片</title>\n    <g id="icon-icon_import_页面" stroke-width="1" fill-rule="evenodd">\n        <g id="icon-icon_import_仿真与故障恢复建议_工况列表_配置参数仿真（InfeedBreaker）" transform="translate(-1162.000000, -620.000000)" fill-rule="nonzero">\n            <g id="icon-icon_import_编组-12" transform="translate(288.000000, 548.000000)">\n                <g id="icon-icon_import_button/primary_icon备份-6" transform="translate(862.000000, 63.000000)">\n                    <g id="icon-icon_import_Icon/import" transform="translate(12.000000, 9.000000)">\n                        <polygon id="icon-icon_import_路径" transform="translate(10.062500, 10.892888) scale(-1, 1) translate(-10.062500, -10.892888) " points="10.7866171 8.58448473 13.125 10.8928884 10.7866171 13.201292 10.1719032 12.5785948 11.391625 11.3748597 7 11.375 7 10.5 11.48175 10.4998597 10.1719032 9.20718194" />\n                        <path d="M11.375,7.875 L10.5,7.875 L10.5,5.25 L7,5.25 L7,1.75 L1.75,1.75 L1.75,12.25 L7,12.25 L7,13.125 L0.875,13.125 L0.875,0.875 L7.875,0.875 L11.375,4.375 L11.375,7.875 Z M7.875,2.113125 L7.875,4.375 L10.136875,4.375 L7.875,2.113125 Z" id="icon-icon_import_形状结合" />\n                    </g>\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},91437:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_warning",use:"icon-icon_warning-usage",viewBox:"0 0 24 24",content:'<symbol viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_warning">\n    <title>切片</title>\n    <g id="icon-icon_warning_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_warning_仿真与故障恢复建议_工况列表_查看详情_同步配置数据" transform="translate(-784.000000, -468.000000)" fill="#FA8405">\n            <g id="icon-icon_warning_编组" transform="translate(760.000000, 444.000000)">\n                <g id="icon-icon_warning_Icon/warning-filled" transform="translate(24.000000, 24.000000)">\n                    <path d="M12,1.5 C17.7989899,1.5 22.5,6.20101013 22.5,12 C22.5,17.7989899 17.7989899,22.5 12,22.5 C6.20101013,22.5 1.5,17.7989899 1.5,12 C1.5,6.20101013 6.20101013,1.5 12,1.5 Z M12,15.375 C11.3786797,15.375 10.875,15.8786797 10.875,16.5 C10.875,17.1213203 11.3786797,17.625 12,17.625 C12.6213203,17.625 13.125,17.1213203 13.125,16.5 C13.125,15.8786797 12.6213203,15.375 12,15.375 Z M12.9375,6.75 L11.0625,6.75 L11.0625,13.5 L12.9375,13.5 L12.9375,6.75 Z" id="icon-icon_warning_形状结合" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l},91581:function(e,t,n){"use strict";var i=n(38006),o=n(50443),a=n(55787),s=n(53649),l=n(68965),r={name:"en",el:{breadcrumb:{label:"Breadcrumb"},colorpicker:{confirm:"OK",clear:"Clear",defaultLabel:"color picker",description:"current color is {color}. press enter to select a new color.",alphaLabel:"pick alpha value"},datepicker:{now:"Now",today:"Today",cancel:"Cancel",clear:"Clear",confirm:"OK",dateTablePrompt:"Use the arrow keys and enter to select the day of the month",monthTablePrompt:"Use the arrow keys and enter to select the month",yearTablePrompt:"Use the arrow keys and enter to select the year",selectedDate:"Selected date",selectDate:"Select date",selectTime:"Select time",startDate:"Start Date",startTime:"Start Time",endDate:"End Date",endTime:"End Time",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",year:"",month1:"January",month2:"February",month3:"March",month4:"April",month5:"May",month6:"June",month7:"July",month8:"August",month9:"September",month10:"October",month11:"November",month12:"December",week:"week",weeks:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},weeksFull:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"},months:{jan:"Jan",feb:"Feb",mar:"Mar",apr:"Apr",may:"May",jun:"Jun",jul:"Jul",aug:"Aug",sep:"Sep",oct:"Oct",nov:"Nov",dec:"Dec"}},inputNumber:{decrease:"decrease number",increase:"increase number"},select:{loading:"Loading",noMatch:"No Matching Data",noData:"No Data",placeholder:"Select"},mention:{loading:"Loading"},dropdown:{toggleDropdown:"Toggle Dropdown"},cascader:{noMatch:"No Matching Data",loading:"Loading",placeholder:"Select",noData:"No Data"},pagination:{goto:"Go to",pagesize:"/page",total:"Total {total}",pageClassifier:"",page:"Page",prev:"Go to previous page",next:"Go to next page",currentPage:"page {pager}",prevPages:"Previous {pager} pages",nextPages:"Next {pager} pages",deprecationWarning:"Deprecated usages detected, please refer to the el-pagination documentation for more details"},dialog:{close:"Close this dialog"},drawer:{close:"Close this dialog"},messagebox:{title:"Message",confirm:"OK",cancel:"Cancel",error:"Illegal input",close:"Close this dialog"},upload:{deleteTip:"press delete to remove",delete:"Delete",preview:"Preview",continue:"Continue"},slider:{defaultLabel:"slider between {min} and {max}",defaultRangeStartLabel:"pick start value",defaultRangeEndLabel:"pick end value"},table:{emptyText:"No Data",confirmFilter:"Confirm",resetFilter:"Reset",clearFilter:"All",sumText:"Sum"},tour:{next:"Next",previous:"Previous",finish:"Finish"},tree:{emptyText:"No Data"},transfer:{noMatch:"No Matching Data",noData:"No Data",titles:["List 1","List 2"],filterPlaceholder:"Enter keyword",noCheckedFormat:"{total} items",hasCheckedFormat:"{checked}/{total} checked"},image:{error:"FAILED"},pageHeader:{title:"Back"},popconfirm:{confirmButtonText:"Yes",cancelButtonText:"No"},carousel:{leftArrow:"Carousel arrow left",rightArrow:"Carousel arrow right",indicator:"Carousel switch to index {index}"}}},c=n(68265),u=n(60747),d=n(15461),m=(0,o.pM)({__name:"App",setup(e){window._mtr_webRefreshToken=e=>{(0,d.T7)()};const t=(0,c.A)(),n=((0,u.A)(),(0,a.KR)(r));(0,o.wB)(()=>t.lang,e=>{i(e)}),(0,o.sV)(()=>{i(t.lang)});const i=e=>{n.value="zh_CN"==e?s.A:"en_US"==e?r:l.A},m=(e,t)=>{let n;return function(...i){const o=self;n&&clearTimeout(n),n=setTimeout(()=>{e.apply(o,i)},t)}},g=window.ResizeObserver;return window.ResizeObserver=class extends g{constructor(e){e=m(e,20),super(e)}},(e,t)=>{const i=(0,o.g2)("router-view"),a=(0,o.g2)("el-config-provider");return(0,o.uX)(),(0,o.Wv)(a,{locale:n.value},{default:(0,o.k6)(()=>[(0,o.bF)(i)]),_:1},8,["locale"])}}});const g=m;var p=g,f=n(77543),h=n(5638);const w=(0,h.Ey)();var v=w,_=n(60802),y=(n(13057),n(87910)),C=n(37992);const b={class:"svg-icon"},L=["xlink:href"];var T=(0,o.pM)({__name:"index",props:{name:{default:""}},setup(e){const t=e;return(e,n)=>((0,o.uX)(),(0,o.CE)("svg",b,[(0,o.Lk)("use",{"xlink:href":`#icon-${t.name}`},null,8,L)]))}}),k=n(48057);const x=(0,k.A)(T,[["__scopeId","data-v-415d4c5c"]]);var S=x;n(66040),n(48773);function D(){try{const e=n(72922);e.keys().forEach(e)}catch(e){console.log(e)}}var A=n(62916),M="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApVJREFUWEfdl1tPE0EUx2fmTC+2qUSE4p1IUAOJL/pZ+hV4kFQ0rRQouh3TNi2JCqL46ns/joEHMEQlAcQrlUq7zJkxi5T0vhi3buK+bLKX8/+dy5w5Q8nvi07MZMIMsc9U6AMAevTc0Rsiai+DigL4vJBN7hBC9KHQnSljmBPocVTNxpgkuLuUE29pPJ4Km5xd/pfiVS2vVBs0GjNGqQdOuQGgD3Cfjj8wbnUr53ZOWTVB7049vm33YTfftwewqlWaX/2Kyp9cn+5WkbYEUFSVznr1qhBCVb2PTmf7qZZXnI5GS4CiD5dfC1FuFBufzl4HLUNOQjQBgCblZ/lHy61EJmYyA1rhpf8bwPLO1RRYAK4X4WGO/3AZMgYomdoFJUNageekdeJII2IMtFfytbm5xI9oNOoDf++IAoCTQDgCoBRuvZgTm1XB+4bRixW46hgAANsulWXFx9lFSgmvNUw53VtIP1y19vba57FkevAAVZ8dhG0EGMEv8znxzjKUSOR69qk5XDUqEbE/QFaEEGajkGEYrFiGEaTE3wmiI4DSxPy4/malUChg1ch4Mj0IR54dEM/6q9z0t3YCY2OpgK+X3aCEsHbfdATQJVxbXBTF2p8jkQgMDN0c1ZIVXz6ZfW8X4ngqFTbL7QeetgAc2M7TzOxGK4F4PB4MBoP7tZtVJ5BOI19rAKYqZzzayu3xbmjnaaf3hmHw7yUyqnlzf2gCYIjaDJDVJSH2/ka08d/JyXzI5PKaUlg3cTdHQHs/Pc9PfXBS/LiAY+lB8NQvzeYIMNiczya3ugFwbyZzXim8UNdHGmdCa20HA2RbUSqdhGAlzUuEnOMNLdq2ETkJ0cqW+2O56wcT149mVl5cPZweFYZrx/NftqFcshvtjbgAAAAASUVORK5CYII=";function P(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle(e,null)[t]}const E={mounted(e,t,n,i){function o(n){if(e.contains(n.target))return!1;"function"===typeof t.value&&t.value(n)}e.__vueClickOutside__=o,document.addEventListener("click",o)},beforeUnmount(e){document.removeEventListener("click",e.__vueClickOutside__),delete e.__vueClickOutside__}},R={mounted(e,t,n,i){const o=t.value,a=e.querySelector(".hs-modal-header"),s=e.querySelector(".hs-modal-wrapper"),l=function(){const e=window.document;return e?.currentStyle?(e,t)=>e.currentStyle[t]:(e,t)=>getComputedStyle(e,null)[t]}();function r(e){const t=e.clientX-a.offsetLeft,n=e.clientY-a.offsetTop,i=document.body.clientWidth,o=document.documentElement.clientHeight,r=s.offsetWidth,c=s.offsetHeight,u=s.offsetLeft,d=i-s.offsetLeft-r,m=s.offsetTop,g=o-s.offsetTop-c;let p=l(s,"left"),f=l(s,"top");p.includes("%")?(p=+document.body.clientWidth*(+p.replace(/%/g,"")/100),f=+document.body.clientHeight*(+f.replace(/%/g,"")/100)):(p=+p.replace(/\px/g,""),f=+f.replace(/\px/g,"")),document.onmousemove=function(e){let i=e.clientX-t,o=e.clientY-n;-i>u?i=-u:i>d&&(i=d),-o>m?o=-m:o>g&&(o=g),s.style.cssText+=`;left:${i+p}px;top:${o+f}px;`},document.onmouseout=function(e){document.onmousemove=null,document.onmouseup=null,document.onmouseout=null},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null,document.onmouseout=null}}o&&(a.style.cssText+=";cursor:move;",s.style.cssText+="",e.__vueModalDragEl__=a,e.__vueModalDrag__=r,e.__vueModalDragEl__.addEventListener("mousedown",e.__vueModalDrag__))},beforeUnmount(e){e.__vueModalDragEl__?.removeEventListener("mousedown",e.__vueModalDrag__),delete e.__vueModalDrag__}},I={beforeMount(e,t){console.log("....copy",t,e.innerHTML),e.targetContent=t.value||e.innerHTML||"";const n=t.arg;function i(){if(!e.targetContent)return console.warn("没有需要复制的目标内容");const t=document.createElement("textarea");t.readOnly=!0,t.style.position="fixed",t.style.top="-99999px",t.value=e.targetContent,document.body.appendChild(t),t.select();const i=document.execCommand("Copy");i&&(console.log("复制成功，剪贴板内容：",e.targetContent),n&&"function"===typeof n&&n(e.targetContent)),document.body.removeChild(t)}e.__HsVueCopy__=i,e.addEventListener("click",e.__HsVueCopy__)},updated(e,t){e.targetContent=t.value},beforeUnmount(e){e.removeEventListener("click",e.__HsVueCopy__),delete e.__HsVueCopy__}},j={mounted(e,t){e.style.cursor="move";const n=t.arg||null;let i=window.innerWidth,o=window.innerHeight;if(n){const t=document.getElementById(n),{width:a,height:s}=t.getBoundingClientRect();i=a,o=s,["fixed","absolute","relative"].includes(P(t,"position"))||(t.style.position="relative"),e.style.position="absolute"}function a(t){const{width:n,height:a}=e.getBoundingClientRect(),s=e.offsetLeft,l=e.offsetTop,r=t.clientX,c=t.clientY,u=s,d=i-s-n,m=l,g=o-l-a;document.onmousemove=t=>{const n=t.clientX-r,i=t.clientY-c;return e.style.left=n<0&&n<=-u?s-u+"px":n>0&&n>=d?s+d+"px":s+n+"px",e.style.top=i<0&&i<=-m?l-m+"px":i>0&&i>=g?l+g+"px":l+i+"px",!1},document.onmouseup=()=>{document.onmousemove=null,document.onmouseup=null}}e.__HsVueDraggable__=a,e.addEventListener("mousedown",e.__HsVueDraggable__)},beforeUnmount(e){e.removeEventListener("click",e.__HsVueDraggable__),delete e.__HsVueDraggable__}};function F(e){return new Promise(t=>{let n=new Image;n.src=e,n.onload=()=>{n.complete&&(t(!0),n=null)},n.onerror=()=>{t(!1),n=null}})}const z={async beforeMount(e,t){const n=t.value;if(e.setAttribute("src",M),n){const t=await F(n);if(t)return void e.setAttribute("src",n)}}};function B(e){try{IntersectionObserver?e.__hsVueEventHandler__.disconnect():window.removeEventListener("scroll",e.__hsVueEventHandler__)}catch{}delete e.__hsVueImgSrc__,delete e.__hsVueImgIsIntersecting__,delete e.__hsVueEventHandler__}function N(e){const t=e.getAttribute("src"),n=e.__hsVueImgSrc__;return t===n}function V(e){const t=N(e),n=e.__hsVueImgIsIntersecting__;n&&!t&&(e.setAttribute("src",e.__hsVueImgSrc__),B(e))}function O(e){const t=new IntersectionObserver(t=>{const n=t[0].isIntersecting;e.__hsVueImgIsIntersecting__=n,V(e)});t.observe(e),e.__hsVueEventHandler__=t}function U(){const e=document.documentElement.clientHeight,t=document.body.clientHeight;let n=0;return n=t&&e?t<e?t:e:t>e?t:e,n}function H(e){const t=N(e);if(!t){const t=U(),{top:n,bottom:i}=e.getBoundingClientRect(),o=n<t&&i>0;e.__hsVueImgIsIntersecting__=o,V(e)}}function Z(e){H(e);const t=(0,A.CU)(H.bind(null,e),1e3);e.__hsVueEventHandler__=t,window.addEventListener("scroll",e.__hsVueEventHandler__)}const G={beforeMount(e,t){const n=t.value;e.__hsVueImgSrc__=n||"",e.setAttribute("src",M)},mounted(e){IntersectionObserver?O(e):Z(e)},updated(e,t){e.__hsVueImgSrc__=t.value||"",IntersectionObserver?V(e):H(e)},unmounted(e){B(e)}},Q={beforeMount(e,t){console.log(".......hsLongPress",t);const n=t.value;if(e.__hsVueLongPressDuration__=t.arg||2e3,"function"!==typeof n)return console.log("v-longpress指令必须接收一个回调函数");let i=null;const o=(0,A.CU)(t=>{"click"===t.type&&0!==t.button||(t.preventDefault(),null===i&&(i=setTimeout(()=>{n(),i=null},e.__hsVueLongPressDuration__)))},1e3,{leading:!0}),a=(0,A.CU)(e=>{null!==i&&(clearTimeout(i),i=null)},1e3,{leading:!0});e.__hsVueLongPressAddHandler__=o,e.__hsVueLongPressCancelHandler__=a,e.addEventListener("mousedown",o),e.addEventListener("touchstart",o),e.addEventListener("click",a),e.addEventListener("mouseout",a),e.addEventListener("touchend",a),e.addEventListener("touchcancel",a)},updated(e,t){e.__hsVueLongPressDuration__=t.arg||2e3},unmounted(e){e.removeEventListener("mousedown",e.__hsVueLongPressAddHandler__),e.removeEventListener("touchstart",e.__hsVueLongPressAddHandler__),e.removeEventListener("click",e.__hsVueLongPressCancelHandler__),e.removeEventListener("mouseout",e.__hsVueLongPressCancelHandler__),e.removeEventListener("touchend",e.__hsVueLongPressCancelHandler__),e.removeEventListener("touchcancel",e.__hsVueLongPressCancelHandler__),delete e.__hsVueLongPressDuration__}};function W(e,t){const n=new CustomEvent(t,{bubbles:!1,cancelable:!0});e.dispatchEvent(n)}const X=["custom","number","decimal","decimal_2"],K={mounted(e,t){const n=t.arg;t.value;if(!n&&!X.includes(n))return console.warn("使用v-hsInputType 指令需要选定类型,如: ",X.join("/"));e.__hsVueInputTypeHandler__=e=>{const t=e.value;console.log("....格式化",t);let i=null,o=t,a=null;switch(n){case"custom":if(a=e.dataset.rule,!a)return console.warn("使用v-hsInputType:custom 需要指定 data-rule=[xxx]");a=(0,A.Ag)(e.dataset.rule),e.value=o.replace(a,"");break;case"number":i=/[^0-9]/,i.test(o)&&(o=o.replace(i,""),console.log("...newValue",o),e.value=o);break;case"decimal":i=/[^0-9.]/g,i.test(o)?o=o.replace(i,""):(o=o.replace(/\.{2,}/g,"."),o=o.replace(".","$#$").replace(/\./g,"").replace("$#$","."),t.indexOf(".")<0&&""!==o&&(o=parseFloat(o)),t.indexOf(".")>-1&&1===o.length&&(o="")),e.value=o;break;case"decimal_2":i=/[^0-9.]/g,i.test(o)?o=o.replace(i,""):(o=o.replace(/\.{2,}/g,"."),o=o.replace(".","$#$").replace(/\./g,"").replace("$#$","."),o=o.replace(/^(-)*([0-9]+)\.([0-9][0-9]).*$/,"$1$2.$3"),t.indexOf(".")<0&&""!==o&&(o=parseFloat(o)),t.indexOf(".")>-1&&1===o.length&&(o="")),e.value=o;break;default:break}e.__hsVueInputTypeOldValue__=o,W(e,"input")},e.__hsVueInputTypeHandler__(e)},updated(e,t){const n=e.value;n!==e.__hsVueInputTypeOldValue__&&e.__hsVueInputTypeHandler__&&e.__hsVueInputTypeHandler__(e)},unmounted(e,t){delete e.__hsVueInputTypeOldValue__,delete e.__hsVueInputTypeHandler__}},q=["admin","user","order"];function J(e,t){t&&!q.includes(t)&&e.parentNode&&e.parentNode.removeChild(e)}const Y={mounted(e,t){J(e,t.value)},updated(e,t){J(e,t.value)}};function $(e){e.directive("clickOutside",E),e.directive("modalDrag",R),e.directive("hsDraggable",j),e.directive("hsCopy",I),e.directive("hsRealImg",z),e.directive("hsLazyImg",G),e.directive("hsLongpress",Q),e.directive("hsInputType",K),e.directive("hsPermission",Y)}window.console.log=function(){};const ee=(0,i.Ef)(p);for(const[te,ne]of Object.entries(y))ee.component(te,ne);ee.component("svg-icon",S),D(),$(ee),ee.use(_.A),ee.use(v).use(C.A).use(f.A).mount("#app")},95558:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_mount",use:"icon-icon_mount-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_mount"><path d="M904 121.1H201.9c-8.8 0-16 7.2-16 16s7.2 16 16 16H888v229.5H136V137.1c0-8.8-7.2-16-16-16s-16 7.2-16 16V887c0 8.8 7.2 16 16 16h681.7c8.8 0 16-7.2 16-16s-7.2-16-16-16H136V672.7h752v214.2c0 8.8 7.2 16 16 16s16-7.2 16-16V137.1c0-8.9-7.2-16-16-16z m-16 519.6H136V414.5h752v226.2z" fill="currentColor" p-id="2095" /><path d="M474.8 322.3H201.9c-8.8 0-16-7.2-16-16V238c0-8.8 7.2-16 16-16h272.9c8.8 0 16 7.2 16 16v68.2c0 8.9-7.2 16.1-16 16.1z m-256.9-32h240.9V254H217.9v36.3zM474.8 577.4H201.9c-8.8 0-16-7.2-16-16v-68.2c0-8.8 7.2-16 16-16h272.9c8.8 0 16 7.2 16 16v68.2c0 8.8-7.2 16-16 16z m-256.9-32h240.9v-36.2H217.9v36.2zM474.8 821.5H201.9c-8.8 0-16-7.2-16-16v-68.2c0-8.8 7.2-16 16-16h272.9c8.8 0 16 7.2 16 16v68.2c0 8.8-7.2 16-16 16z m-256.9-32h240.9v-36.2H217.9v36.2zM781.7 323.4c-27.6 0-50.1-22.5-50.1-50.1s22.5-50.1 50.1-50.1 50.1 22.5 50.1 50.1-22.5 50.1-50.1 50.1z m0-68.3c-10 0-18.1 8.1-18.1 18.1s8.1 18.1 18.1 18.1c10 0 18.1-8.1 18.1-18.1s-8.2-18.1-18.1-18.1zM781.7 577.4c-27.6 0-50.1-22.5-50.1-50.1s22.5-50.1 50.1-50.1 50.1 22.5 50.1 50.1-22.5 50.1-50.1 50.1z m0-68.3c-10 0-18.1 8.1-18.1 18.1 0 10 8.1 18.1 18.1 18.1 10 0 18.1-8.1 18.1-18.1 0-9.9-8.2-18.1-18.1-18.1zM781.7 820.4c-27.6 0-50.1-22.5-50.1-50.1s22.5-50.1 50.1-50.1 50.1 22.5 50.1 50.1-22.5 50.1-50.1 50.1z m0-68.3c-10 0-18.1 8.1-18.1 18.1 0 10 8.1 18.1 18.1 18.1 10 0 18.1-8.1 18.1-18.1 0-9.9-8.2-18.1-18.1-18.1z" fill="currentColor" p-id="2096" /></symbol>'});s().add(l);t["default"]=l},95688:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_fullscreen",use:"icon-icon_fullscreen-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" id="icon-icon_fullscreen"><path d="M192 384v-96c0-54.4 41.6-96 96-96h96c19.2 0 32-12.8 32-32s-12.8-32-32-32h-96c-89.6 0-160 70.4-160 160v96c0 19.2 12.8 32 32 32s32-12.8 32-32z m-64 256v96c0 89.6 70.4 160 160 160h96c19.2 0 32-12.8 32-32s-12.8-32-32-32h-96c-54.4 0-96-41.6-96-96v-96c0-19.2-12.8-32-32-32s-32 12.8-32 32z m768-256v-96c0-89.6-70.4-160-160-160h-96c-19.2 0-32 12.8-32 32s12.8 32 32 32h96c54.4 0 96 41.6 96 96v96c0 19.2 12.8 32 32 32s32-12.8 32-32z m-64 256v96c0 54.4-41.6 96-96 96h-96c-19.2 0-32 12.8-32 32s12.8 32 32 32h96c89.6 0 160-70.4 160-160v-96c0-19.2-12.8-32-32-32s-32 12.8-32 32z" p-id="3213" /></symbol>'});s().add(l);t["default"]=l},98493:function(e,t,n){"use strict";n.r(t);var i=n(82631),o=n.n(i),a=n(83313),s=n.n(a),l=new(o())({id:"icon-icon_success-filled",use:"icon-icon_success-filled-usage",viewBox:"0 0 24 24",content:'<symbol viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-icon_success-filled">\n    <title>切片</title>\n    <g id="icon-icon_success-filled_页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g id="icon-icon_success-filled_仿真与故障恢复建议_工况列表_查看详情_执行仿真_提交成功" transform="translate(-784.000000, -479.000000)" fill="#16B823">\n            <g id="icon-icon_success-filled_编组" transform="translate(760.000000, 455.000000)">\n                <g id="icon-icon_success-filled_Icon/success-filled" transform="translate(24.000000, 24.000000)">\n                    <path d="M12,1.5 C17.7989899,1.5 22.5,6.20101013 22.5,12 C22.5,17.7989899 17.7989899,22.5 12,22.5 C6.20101013,22.5 1.5,17.7989899 1.5,12 C1.5,6.20101013 6.20101013,1.5 12,1.5 Z M16.3446699,8.09466991 L10.5,13.9395 L7.65533009,11.0946699 L6.59466991,12.1553301 L9.96966991,15.5303301 C10.2625631,15.8232233 10.7374369,15.8232233 11.0303301,15.5303301 L11.0303301,15.5303301 L17.4053301,9.15533009 L16.3446699,8.09466991 Z" id="icon-icon_success-filled_形状结合" />\n                </g>\n            </g>\n        </g>\n    </g>\n</symbol>'});s().add(l);t["default"]=l}},t={};function n(i){var o=t[i];if(void 0!==o)return o.exports;var a=t[i]={id:i,loaded:!1,exports:{}};return e[i].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=e,function(){var e=[];n.O=function(t,i,o,a){if(!i){var s=1/0;for(u=0;u<e.length;u++){i=e[u][0],o=e[u][1],a=e[u][2];for(var l=!0,r=0;r<i.length;r++)(!1&a||s>=a)&&Object.keys(n.O).every(function(e){return n.O[e](i[r])})?i.splice(r--,1):(l=!1,a<s&&(s=a));if(l){e.splice(u--,1);var c=o();void 0!==c&&(t=c)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[i,o,a]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce(function(t,i){return n.f[i](e,t),t},[]))}}(),function(){n.u=function(e){return"static/js/"+e+"."+{279:"f06f58e7",280:"e76b20f2",540:"aa7d6d68",668:"ee573203",1056:"8131e301",1232:"b192c6f6",1647:"14b560b9",1877:"81812236",2039:"811148be",2058:"c17c416e",2213:"c967f7f3",2283:"6fec4cd6",2305:"c4416c84",2409:"3f0c011e",2895:"4a2c227d",3215:"bbab34f4",3284:"7c03e1c0",3351:"739ecf9b",3570:"475fd2ff",3754:"c66ffd43",3945:"8a9b0c9b",4383:"e45dd30c",4707:"67a4ad41",4719:"5748a880",5273:"a889cb2d",5301:"2fcb80f4",5495:"53a3c01a",5829:"e013c200",6008:"afa4029d",6341:"01338c7f",6399:"af226705",6414:"d4dd11f8",6988:"07e7b965",7023:"4d9560ea",7365:"56a75e04",7655:"647abc50",7768:"6323fd47",7872:"4ef24112",7939:"fa75a88a",7966:"7e3c31ba",8499:"c1e056e6",8540:"e82944e5",8896:"8463af3e",9018:"b827267c",9250:"fd39b73c",9265:"8269c7e0",9707:"cc150939",9892:"defd9a79",9998:"06a367ad"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"static/css/"+e+"."+{280:"876cc1f9",540:"4f0dd30a",668:"96eb093e",981:"cc06c260",1056:"6cbf2cde",1232:"3aaf3d00",1647:"3f4e8226",1877:"f4e2c82a",2039:"34761ca8",2058:"b6b5d38c",2213:"76bc447c",2283:"560abc91",2409:"16b5e81a",2895:"ae0c4e69",3215:"9909e296",3284:"af9bcc35",3351:"1cdfca29",3570:"2e0b66c6",3754:"93f2964c",3945:"2cc9894b",4383:"b097afb8",4707:"2615144a",4719:"fb6c46f7",5273:"97b42775",5495:"95936478",5829:"7d5967e5",6008:"a3d6ded1",6341:"a23712a4",6399:"489a6d6b",6414:"936070b0",6988:"57769b5b",7023:"49bcbc4a",7365:"0037e90d",7655:"6c8a0dbe",7768:"adcd69ab",7872:"9280445f",7939:"4de8f17b",7966:"fb704ea5",8499:"4ac89156",8540:"50bbf7f3",8896:"ec903966",9018:"45934427",9250:"65e71701",9265:"ab85a0e7",9707:"339e19bc",9892:"440708dc"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="system-mtr:";n.l=function(i,o,a,s){if(e[i])e[i].push(o);else{var l,r;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==i||d.getAttribute("data-webpack")==t+a){l=d;break}}l||(r=!0,l=document.createElement("script"),l.charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.setAttribute("data-webpack",t+a),l.src=i),e[i]=[o];var m=function(t,n){l.onerror=l.onload=null,clearTimeout(g);var o=e[i];if(delete e[i],l.parentNode&&l.parentNode.removeChild(l),o&&o.forEach(function(e){return e(n)}),t)return t(n)},g=setTimeout(m.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=m.bind(null,l.onerror),l.onload=m.bind(null,l.onload),r&&document.head.appendChild(l)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p="/mtr-sim/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,i,o,a){var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",n.nc&&(s.nonce=n.nc);var l=function(n){if(s.onerror=s.onload=null,"load"===n.type)o();else{var i=n&&n.type,l=n&&n.target&&n.target.href||t,r=new Error("Loading CSS chunk "+e+" failed.\n("+i+": "+l+")");r.name="ChunkLoadError",r.code="CSS_CHUNK_LOAD_FAILED",r.type=i,r.request=l,s.parentNode&&s.parentNode.removeChild(s),a(r)}};return s.onerror=s.onload=l,s.href=t,i?i.parentNode.insertBefore(s,i.nextSibling):document.head.appendChild(s),s},t=function(e,t){for(var n=document.getElementsByTagName("link"),i=0;i<n.length;i++){var o=n[i],a=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(a===e||a===t))return o}var s=document.getElementsByTagName("style");for(i=0;i<s.length;i++){o=s[i],a=o.getAttribute("data-href");if(a===e||a===t)return o}},i=function(i){return new Promise(function(o,a){var s=n.miniCssF(i),l=n.p+s;if(t(s,l))return o();e(i,l,null,o,a)})},o={3524:0};n.f.miniCss=function(e,t){var n={280:1,540:1,668:1,981:1,1056:1,1232:1,1647:1,1877:1,2039:1,2058:1,2213:1,2283:1,2409:1,2895:1,3215:1,3284:1,3351:1,3570:1,3754:1,3945:1,4383:1,4707:1,4719:1,5273:1,5495:1,5829:1,6008:1,6341:1,6399:1,6414:1,6988:1,7023:1,7365:1,7655:1,7768:1,7872:1,7939:1,7966:1,8499:1,8540:1,8896:1,9018:1,9250:1,9265:1,9707:1,9892:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=i(e).then(function(){o[e]=0},function(t){throw delete o[e],t}))}}}(),function(){var e={3524:0};n.f.j=function(t,i){var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)i.push(o[2]);else if(981!=t){var a=new Promise(function(n,i){o=e[t]=[n,i]});i.push(o[2]=a);var s=n.p+n.u(t),l=new Error,r=function(i){if(n.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var a=i&&("load"===i.type?"missing":i.type),s=i&&i.target&&i.target.src;l.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",l.name="ChunkLoadError",l.type=a,l.request=s,o[1](l)}};n.l(s,r,"chunk-"+t,t)}else e[t]=0},n.O.j=function(t){return 0===e[t]};var t=function(t,i){var o,a,s=i[0],l=i[1],r=i[2],c=0;if(s.some(function(t){return 0!==e[t]})){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(r)var u=r(n)}for(t&&t(i);c<s.length;c++)a=s[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(u)},i=self["webpackChunksystem_mtr"]=self["webpackChunksystem_mtr"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))}();var i=n.O(void 0,[504],function(){return n(91581)});i=n.O(i)})();
//# sourceMappingURL=app.b0bb5815.js.map