import * as React from 'react';
import type { LayoutProps } from '../props/layout.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GridOwnProps } from './grid.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface CommonGridProps extends MarginProps, LayoutProps, GridOwnProps {
}
type GridDivProps = {
    as?: 'div';
} & ComponentPropsWithout<'div', RemovedProps>;
type GridSpanProps = {
    as: 'span';
} & ComponentPropsWithout<'span', RemovedProps>;
type GridProps = CommonGridProps & (GridSpanProps | GridDivProps);
declare const Grid: React.ForwardRefExoticComponent<GridProps & React.RefAttributes<HTMLDivElement>>;
export { Grid };
export type { GridProps };
//# sourceMappingURL=grid.d.ts.map