{"version": 3, "sources": ["../../../src/components/em.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Slot } from 'radix-ui';\n\nimport { emPropDefs } from './em.props.js';\nimport { extractProps } from '../helpers/extract-props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype EmElement = React.ElementRef<'em'>;\ntype EmOwnProps = GetPropDefTypes<typeof emPropDefs>;\ninterface EmProps extends ComponentPropsWithout<'em', RemovedProps>, EmOwnProps {}\nconst Em = React.forwardRef<EmElement, EmProps>((props, forwardedRef) => {\n  const { asChild, className, ...emProps } = extractProps(props, emPropDefs);\n  const Comp = asChild ? Slot.Root : 'em';\n  return <Comp {...emProps} ref={forwardedRef} className={classNames('rt-Em', className)} />;\n});\nEm.displayName = 'Em';\n\nexport { Em };\nexport type { EmProps };\n"], "mappings": "AAAA,UAAYA,MAAW,QACvB,OAAOC,MAAgB,aACvB,OAAS,QAAAC,MAAY,WAErB,OAAS,cAAAC,MAAkB,gBAC3B,OAAS,gBAAAC,MAAoB,8BAQ7B,MAAMC,EAAKL,EAAM,WAA+B,CAACM,EAAOC,IAAiB,CACvE,KAAM,CAAE,QAAAC,EAAS,UAAAC,EAAW,GAAGC,CAAQ,EAAIN,EAAaE,EAAOH,CAAU,EACnEQ,EAAOH,EAAUN,EAAK,KAAO,KACnC,OAAOF,EAAA,cAACW,EAAA,CAAM,GAAGD,EAAS,IAAKH,EAAc,UAAWN,EAAW,QAASQ,CAAS,EAAG,CAC1F,CAAC,EACDJ,EAAG,YAAc", "names": ["React", "classNames", "Slot", "emPropDefs", "extractProps", "Em", "props", "forwardedRef", "<PERSON><PERSON><PERSON><PERSON>", "className", "emProps", "Comp"]}