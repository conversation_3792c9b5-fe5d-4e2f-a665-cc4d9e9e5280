import * as React from 'react';
import type { LayoutProps } from '../props/layout.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ContainerOwnProps } from './container.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
interface ContainerProps extends ComponentPropsWithout<'div', RemovedProps>, MarginProps, LayoutProps, ContainerOwnProps {
}
declare const Container: React.ForwardRefExoticComponent<ContainerProps & React.RefAttributes<HTMLDivElement>>;
export { Container };
export type { ContainerProps };
//# sourceMappingURL=container.d.ts.map