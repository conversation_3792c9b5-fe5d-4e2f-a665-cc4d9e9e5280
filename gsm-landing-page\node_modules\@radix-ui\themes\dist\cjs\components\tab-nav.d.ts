import * as React from 'react';
import { NavigationMenu } from 'radix-ui';
import { tabNavRootPropDefs } from './tab-nav.props.js';
import type { tabNavLinkPropDefs } from './tab-nav.props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type TabNavRootElementProps = ComponentPropsWithout<'nav', RemovedProps>;
type TabNavOwnProps = GetPropDefTypes<typeof tabNavRootPropDefs>;
interface TabNavRootProps extends Omit<TabNavRootElementProps, 'defaultValue' | 'dir' | 'color'>, MarginProps, TabNavOwnProps {
}
declare const TabNavRoot: React.ForwardRefExoticComponent<TabNavRootProps & React.RefAttributes<HTMLElement>>;
type TabNavLinkOwnProps = GetPropDefTypes<typeof tabNavLinkPropDefs>;
interface TabNavLinkProps extends ComponentPropsWithout<typeof NavigationMenu.Link, RemovedProps | 'onSelect'>, TabNavLinkOwnProps {
}
declare const TabNavLink: React.ForwardRefExoticComponent<TabNavLinkProps & React.RefAttributes<HTMLAnchorElement>>;
export { TabNavRoot as Root, TabNavLink as Link };
export type { TabNavRootProps as RootProps, TabNavLinkProps as LinkProps };
//# sourceMappingURL=tab-nav.d.ts.map