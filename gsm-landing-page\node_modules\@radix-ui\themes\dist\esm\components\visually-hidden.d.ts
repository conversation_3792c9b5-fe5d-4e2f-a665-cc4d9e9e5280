import { VisuallyHidden as VisuallyHiddenPrimitive } from 'radix-ui';
export declare const VisuallyHidden: import("react").ForwardRefExoticComponent<VisuallyHiddenPrimitive.VisuallyHiddenProps & import("react").RefAttributes<HTMLSpanElement>>;
export declare const Root: import("react").ForwardRefExoticComponent<VisuallyHiddenPrimitive.VisuallyHiddenProps & import("react").RefAttributes<HTMLSpanElement>>;
export type VisuallyHiddenProps = VisuallyHiddenPrimitive.VisuallyHiddenProps;
//# sourceMappingURL=visually-hidden.d.ts.map