{"name": "define-lazy-prop", "version": "3.0.0", "description": "Define a lazily evaluated property on an object", "license": "MIT", "repository": "sindresorhus/define-lazy-prop", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["lazy", "property", "properties", "prop", "define", "object", "value", "lazily", "laziness", "evaluation", "eval", "execute", "getter", "function", "fn", "memoize", "cache", "defer", "deferred"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}