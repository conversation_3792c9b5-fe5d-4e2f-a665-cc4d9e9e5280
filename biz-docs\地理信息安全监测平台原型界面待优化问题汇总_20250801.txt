时空数据安全监测平台系统功能汇总（政府端）

注：

模块分为登录、综合概览、备案审核注册信息管理、实时监控、监督检查、系统管理，通过顶端导航栏区分；

二级子模块通过每个导航栏左侧的功能栏区分，左侧功能栏随顶部导航切换动态切换；

三级子模块设置左侧的功能栏二级子模块的下一级；

具体导航栏和功能栏的可见、可编辑配置，可以通过账户的权限管理进行配置； 模块 二级子模块 三级功能模块 功能描述 关键字段信息 备注 功能参考 修改建议 示例

整体（包含所有界面）

1、所有政府端和企业端的界面字体大小，排版及风格，参照示例标准执行

2、所有政府端和企业端设涉及到对话框的内容，先填充完整字段信息，不要

空着

登陆

注册

—

填写注册信息，进行账号注册 通用功能 政府用户和企业用户登录时的界面显示，都统一为企业用户的样式

登陆 输入用户名、账号检验登陆 用户名、密码、校验码

找回密码 忘记密码后可通过邮箱、电话、身份证等找回 通用功能

点击×号无法关闭；调整布局，忘记密码需要设置成TAB页形式；前置提示词

美化；

大屏

大屏基于【综合概览】的主要信息，结合发到群里的大屏参

考界面以及之前行业经验设计

综合概览

(首页) 属地试点城市区域地图显示（中上）

—

最顶部显示：车辆总数、在线车辆总数、累计车端风险总数、累计云端风险总数

动态展示地理围栏范围，支持缩放、拖拽，要高清；

实时标记车端和云端分布节点（在区域内简单用通用图标展示即可，鼠标悬停或点击时

要显示基本信息），通过不同图标区分；

试点城市区域图（天津为例）：增加地理信息围栏（参照F列）

支持监控大屏协作模式（支持多监管员协同）；

同时可以再设计一些高级功能（鼠标悬停关键信息显示、点击图

表可联动地图定位相关区域等）；

违规企业特殊标注（如存在未处理的安全风险，通过颜色红、橙

、黄区分）

风险热力图；

1、车辆总数改为【接入车辆总数】

2、所有的描述字体样式保持一致

3、【累计车端风险】后边增加一个【车端累计上报事件】

4、【累计云端风险】后边增加一个【云端累计上报事件】

5、鼠标悬停提示功能，尽量所有的图标都要加上这个功能

处理活动信息（中下）

分栏显示“车端操作日志”（左侧）与“云端操作日志”（右侧）；

支持按照时间过滤显示；同时两个界面定时切换显示。

车端：序号、VIN码（保密起见-对应一个车辆ID？or 按照权限显示1111***1111）、

车辆品牌、车辆型号、处理阶段（收集、存储、传输）、处理时间

云端：序号、企业名称、企业类型（地图服务商、汽车企业、智驾方案提供商、平台

运营商、处理阶段（收集、存储、传输、加工、提供、公开、销毁）处理时间

1、因为平台本身会对收集的数据进行存储，并不是所有字段都

需要显示在这里，需要斟酌

2、在首页中是否要增加过滤查询功能，结合行业经验设计

1、左侧改为（车端）【风险预警】，右侧改为（云端）【风险预警】

2、因为现在下端分成了左右两部分，看如何作一些边界区分，表征出左侧和

中间的左侧为一个整体，右边为一个整体

车端统计（左侧）

左上：

可以根据车辆品牌、车辆类型（M类、N类、O类、低速无人驾驶装备）显示占比，直观展

示相关信息。

车辆信息：显示车辆类型占比、车辆品牌占比（环形图等）

1、饼状图样式修改，去掉周围的那几条多余环线，注意外围的数字百分比表

示要保留，样式参照示例。

2、颜色可以再考虑考虑，比如考虑港铁中的那种暗红、暗紫、暗橙色，避免

那种亮艳色，色彩搭配可以让AI给点配色建议

左中：

风险统计：按照风险对应数据处理阶段（收集、存储、传输）和风险等级（高、中、

低）统计

1、支持按照时间（日/周/月/年）筛选风险，若需要跨年增加一个下拉菜单支持选

择；

2、支持三种显示样式：柱状图（统计数量：处理阶段和风险等级）、饼图（统计占

比：处理阶段和风险等级）、折线图（风险走势统计：处理阶段和风险等级）；

1、现在改为了【处理活动】：参照示例样式按照收集、存储和传输阶段汇

总；

2、鼠标悬停在曲线上要显示数字，具体精度的话以一小时为刻度进行设计

左下-风险预警：

分级显示车端数据安全风险，风险等级（只在风险等级字段体现红/橙/白），分类分别

对应高/中/低风险；

支持按照年/月/日等按钮点击过滤显示风险告警信息；

对应的安全风险（包括安全风险的详细信息）需要实时自动派送到责任部门；

车端风险列表：序号、VIN码、风险等级（高、中、低）、处理阶段（收集、存储、

传输）、风险事件、告警时间

高风险：闪烁图标+强制弹窗

中风险：呼吸灯效果+消息中心提醒

低风险：静态图标+周期性（当天）汇总报告

结合行业经验设置

现在改为了【风险事件】，分为左右两部分：

1、左半部分：当前的风险统计，将阶段的注释图标放在下边，另外对一下图

标是采用原型还是小方框好看；还有就是颜色搭配能优化则优化；

2、右半部分：事件统计，以饼状图形式进行统计，细分车端事件类型咨询刁

工

云端统计（右侧）

右上：

可以根据企业类型（地图服务商、汽车企业、智驾方案提供商、平台运营方）显示占

比，直观展示相关信息。

企业信息：显示各类企业占比（环形图） 同上 1、样式修改，同车端

右中：

风险统计：按照风险对应数据处理阶段（收集、存储、传输、加工、提供、公开、销

毁）和风险等级（高、中、低）统计

1、支持按照时间（日/周/月/年）筛选风险，若需要跨年增加一个下拉菜单支持选

择；

2、支持三种显示样式：柱状图（统计数量：处理阶段和风险等级）、饼图（统计占

比：处理阶段和风险等级）、折线图（风险走势统计：处理阶段和风险等级）；

1、因为云端涉及到的阶段（收集、存储、传输、加工、提供、公开、销毁）

较多，感觉参照车端方法绘制可能会有点乱，设计一下看该如何体现，也要有

对每个阶段处理活动数量的显示；

右下-风险预警：

分级显示云端数据安全风险，风险等级（只在风险等级字段体现红/橙/白），分类分别

对应高/中/低风险；

支持按照年/月/日等按钮点击过滤显示风险告警信息；

对应的安全风险（包括安全风险的详细信息）需要实时自动派送到责任部门；

云端风险列表：序号、企业名称、风险等级（高、中、低）、操作类型（收集、存储

、传输、加工、提供、公开、销毁理）、风险事件、告警时间

1、当前风险统计中呈现的阶段不全面，需要补充全；

2、当前的阶段比较多，原来的柱状图可能比较乱，试试看吧，找一种效果最

好的呈现形式

3、云端的事件类型划分，咨询刁工

4、其他的和同车端要求

备案审核

注册信息管理 —

为政府主管部门提供全面、直观的数据视图，综合显示已经注册备案的企业（包括汽车

企业、地图服务商、智驾方案提供商、平台运营商）车辆的基本信息，作为企业及车辆

的备案信息库。

显示各类企业的占比（环形图等）；

体现注册中、注册通过、注册未通过企业占比；

支持按照时间、车辆VIN码、企业ID等关键词进行注册填报信息搜索；

支持按照企业一键导出注册备案信息报告。

顶端统计信息显示：

企业信息：企业类型包括地图服务商、汽车企业、智驾方案提供商、平台运营方

显示各类企业占比（环形图占比）；

车辆信息：可以根据车辆品牌、车辆类型（M类、N类、O类、低速无人驾驶装备）显

示占比。

企业基本信息显示：

序号、企业名称（详细信息点击可查看【企业基本信息】）、企业类型（地图服务商

、汽车企业、智驾方案提供商、平台运营方）、注册状态（成功、失败、流程中）、

测绘资质类型（互联网地图服务-甲/乙、导航电子地图制作-甲-/乙、地理信息系统

工程-甲/乙，详细信息点击可查看【测绘资质信息】）、安全防护措施（详细信息点

击可查看【数据安全防护措施】）、数据处理活动目的（数据脱敏处理、场景库制作

及处理、导航电子地图制作、互联网地图服务，详细信息可点击查看【数据处理活动

信息】）、注册时间（最新时间）

车辆基本信息：备案企业、车辆品牌、车型、车辆VIN码、应用的地理信息安全处理

技术（脱敏技术、坐标偏转技术、VMS数据融合技术、地理信息围栏技术，点击可查

看【技术详细信息】，每个技术要增加对应的说明信息，如采用的是插件还是算法、

是自研还是地方机构开发，技术认证情况等）、传感器系统信息（看是否需要按照标

准梳理？？？）、投入数量等。

注册备案信息搜索：按照各个企业基本信息单独设置搜索条件

一键导出：支持按照各个企业基本信息单独导出、整体导出（需要重点考虑如何将点

击后查看到的详细信息同步导出）；

点击之后需要查看的详细信息参照企业端注册时的关键字段信息。

企业信用评估模型：整合备案合规率（30%）+风险发生率

（40%）+整改及时性（30%）动态生成信用等级，直接关系抽查

概率（暂时先不实现）

催办触发（≥3天未审批）。

1、【企业基本信息】改为【企业信息】，过滤框只需要保留企业名称、企业

类型、注册状态、注册时间；

2、【导出EXCEL】功能实现，界面按照通用样式设计

3、表格的表头顺序及内容为：序号、企业名称、企业类型、注册状态、企业

基本信息、测绘资质信息、车辆信息、安全防护措施、处理活动信息、注册时

间、操作

4、点击查看详情后的界面：按照下边【备案审批】中的样式作调整，也是做

成TAB页形式，样式保持一致；每个tab页中把下边I20表中梳理的内容呈现，

把审批记录也迁移过来；【下一步】改为【下一页】，增加【上一页】，实现

上下页切换

5、审批信息：表头为企业基本信息、测绘资质信息、车辆信息、安全防护措

施、处理活动信息、申请人、申请时间、审批人、审批时间，下边的成功/失

败改为通过/驳回；点击关闭按钮没反应。

备案审核

备案审批 —

可以调用模版发起政策及标准发布申请；

显示政策标准通知，记录时空数据安全监管相关的政策和标准，同时支持查看、搜索、

编辑、删除等功能，新增更新时自动通知相关企业。

我的申请：对应政策和标准下发申请对应的模版；

通知：政策和标准对应的搜索栏、新增、查看、编辑、删除、发布等。

催办触发（≥3天未审批）。

1、政策标准发布这个功能整体迁移到【系统管理】导航栏下，在最下边新增

加一个左侧的功能导航栏【信息发布】

2、【政策标准】修改为【信息发布】；【通知公告】修改为【信息类型】；

【状态】包括待发布、已发布；【操作】因为涉及到多个，建议换成【...】

点击之后弹出一个下拉菜单，包括发布、编辑、删除，具体点击不同的操作时

要有不同的跳转，编辑-跳转到编辑页面，支持对信息进行编辑，最下边要有

【保存】【发布】等按钮，删除和发布按钮点击之后要有一个提示框，提示操

作人员是否确定当前的删除/发布操作，并且提示框中要有关闭、确定、取消

等按钮，提示框按照通用格式设计。

待办任务：集中显示企业的申请列表，为政府主管部门提供高效、便捷的申请处理功

能，支持对各类申请集中管理和审批，可以点击查看每条申请的详细信息（详细信息为

企业注册时的申请信息）；审批结果自动加盖监管部门电子章，生成PDF回执（含二维码

供企业扫码验证）反馈给企业，对满足申请条件的首次注册或更新的车辆及企业自动纳

入车辆及企业信息库。

任务列表：序号、企业名称、申请类型（申诉处理、新注册、注册更新、风险评估）

、申请材料（可点击查看详细信息）、申请时间、当前状态、操作（...右键可点击

选择通过、驳回等），点击可查看详细信息。

审批界面：勾选常见问题（见备注）、备注批注框、通过/拒绝等。 支持根据关键字段进行模糊搜索

审批日志上链存证（其他类似不能修改的信息均要上链，防止篡

改）

智能预审功能：对企业填报的信息进行自动智能化审核（只是对

要求的内容全不全进行审核，不对每项具体的内容进行审核），

并给出一个初审结果供操作人参考，降低人工审核的工作量。

审批界面：

车辆备案审批：车辆基本信息缺失、传感系统精度过高、安全防

护技术不满足要求（偏转插件不满足要求、脱敏脱密插件不满足

要求）；、备注批注框、通过/拒绝；

企业备案审批：企业基本信息（营业执照超过有效期、关键信息

缺失、备注批注框）、测绘资质信息（资质类别等级与开展活动

不符、资质超过有效期、关键信息缺失、备注批注框）、安全防

护措施（***管理制度缺失、防护技术清单不完善）、备注批注

框；通过/拒绝；

处理活动审批：活动区域超限、数据目录缺少等、备注批注框；

通过/拒绝；

要结合注册备案信息自动生成审批记录。

1、当前这个框整体迁移到上边

2、待办任务下边需要增加过滤框，包括企业名称、统一社会信用代码、申请

类型（下拉菜单形式）、申请时间（可选择开始结束时间）

3、去掉【当前状态】，【操作】中的“审批”改为“查看详情息”

4、点击查看详情后，弹出的界面样式需要调整：最上边的框保持现状，但是

可以点击可以切换的功能可以去掉；按照5个信息分类，以tab页的形式呈现，

每个tab页可以随时点击切入；

5、每个tab页下边的【保存】按钮保存，用于保存对当前tab页的审批结果，

再最终整体审批前都可以修改审批结果；【下一步】改为【下一页】，同时增

加【上一页】，支持上下页切换；

6、测绘资质信息页：测绘资质类别与等级可以做成表格的形式，参照后边

sheet页

7、车辆信息页：【序号】【车辆品牌】【车型】【投入数量】【车辆VIN码清

单】（附件形式）【应用的地理信息安全处理技术】改为【安全处理技术清单

】（附件形式，里边会包含技术说明）；【应用的地理信息安全处理技术说明

】去掉；【传感器系统信息】改为【自动驾驶系统信息】（附件形式）；拒绝

原因：关键信息缺失，备注里可以说明一下具体哪个信息缺失；

8、数据安全防控措施信息页：【组织架构】修改为【组织架构（数据安全）

】，下边每个信息前的“信息安全”字样去掉；【数据安全组织架构管理制度

】改为【管理制度】，下边的【数据分类分级管理】改为【分类分级】，【安

全风险评估】改为【风险评估】；【监测预警与应急管理】拆成【监测预警】

与【应急管理】两个，【车端安全防护技术】改为【防护技术】，下边再细分

【车端】和【云端】；【拒绝原因】包括组织架构不完善、管理制度缺失、防

护技术不完善，备注里可以进一步描述具体是哪些不满足要求。

9、数据处理活动信息页：【数据采集区域及路线】改为【活动区域及路线

】，去掉【不区分示范区】，若选择的是【示范区部分区域】则需要上传活动

区域及路线说明材料（附件形式，一个整体文件就行，不需要拆成2个）；【

数据处理活动方式】改为【活动方式】，选项改为【独自】和【合作】，若选

择合作形式，需要填写合作单位信息，信息字段分别为单位名称、统一社会信

已办任务：显示已经完成的任务列表，可以点击查看已经完成的任务信息。

任务列表：序号、企业名称、申请类型（申诉处理、新注册、注册更新）、申请材料

（可点击查看详细信息）、申请时间、当前状态、操作人员、处理时间。

审批记录：点击之后可以查看详细信息，除了上述字段外，还应包括审批流程（电子

签名、签名日期、操作、留言）。

1、【当前状态】改为【审批时间】，在后边增加【审批结果】包括同意/驳

回，【操作】下去掉查看详情功能，只保留审批记录，点击弹框信息只保留审

核，去掉预审，【提交】改为【关闭】，没有需要提交的业务流，上边要有×

2、去掉全部任务和待阅任务tab页

发起跟踪：显示发起的任务审批列表，可以点击查看的当前的任务审批流程

任务列表：序号、类型（备案申请/申述处理/政策标准发布/安全风险评估）、标题

、提交时间、当前待办人；

审批记录：序号、节点名称、电子签名、签名日期、操作、留言

自动生成流程编号，具体模版待设计。

考虑到人员不足等问题，没有设置待阅/已阅任务

实时监测

安全风险管理

车端风险

为政府主管部门提供全面、直观的数据视图，显示车端产生的安全风险基本信息，同时

显示各类风险的占比（环形图等）、风险处置情况（处置中、未处置、已处置）；

支持按照处置状态、处理阶段、风险等级等分类原则进行统计显示；

支持按照时间、车辆VIN码、车辆品牌、企业ID等关键词进行安全风险搜索；

支持一键导出安全风险信息报告；

鼠标点击每条风险，可以弹出信息框，显示风险的详细信息；

车端安全风险：

告警类型：告警类型分为按照采集、存储、传输

风险搜索：支持按照时间、车辆VIN码、风险类型、注册企业等进行安全风险搜索

风险详细信息：时间、注册企业、车辆品牌、车辆VIN码、风险类型、风险危害程度

、风险发生可能性、风险等级、当前状态、处置建议等；

支持一键导出安全风险信息报告；

风险类型：泄露、篡改、破坏、丢失、滥用、伪造、其他风险

1、上端风险统计框：首先柱状图不要用渐变色，港铁中的颜色就可以，因为

本身背景是白色，可以尝试配比其他颜色，必要时用AI辅助设计颜色搭配；处

理阶段示意标识看是用原型还是方框美观；增加风险处置率的统计（分阶段、

分等级统计）；样式太单调，结合要显示的内容以及各类图表的表达作用，想

想怎么丰富一些；整体框可以下拉扩大一些，布局还需美化设计。

2、下端信息检索框：

（1）检索信息顺序及内容包括注册企业、车辆VIN码、车辆品牌、车型、处理

阶段（下拉列表）、处理状态（下拉列表已处理、处理中、未处理）、风险等

级（下拉列表，高风险、中风险、低风险）、发生时间（可以选择起止时

间）；

（2）风险信息表头前后顺序及内容包括注册企业、车辆VIN码、车辆品牌、车

型、处理阶段、风险等级、风险描述、当前状态（已处理、处理中、未处理）

、详细信息（下边内容为查看详情）、风险溯源（下边内容为溯源追踪）、发

生时间、处置完成时间；

（3）点击详细信息后的弹弹框界面，在包含（2）中表头信息的基础上，主要

是对风险信息的详细描述，包括风险危害程度、风险发生可能性、处置建议

等，还需要其他哪些信息和刁工一起商议，点击后详细信息框参照示例样式。

（4）导出安全风险信息报告：改为【风险信息导出】，具体逻辑按照通用的

报告导出的样式设计

云端风险 同上

云端安全风险：

告警类型：告警类型分收集、存储、传输、加工、提供、公开

风险搜索：支持按照时间、企业名称、风险类型等进行安全风险搜索

风险详细信息：时间、企业名称、企业类型（汽车车企、地图服务商、智驾方案提供

商、平台运营公司）、风险类型、风险危害程度、风险发生可能性、风险等级、、当

前状态、处置建议等；

支持一键导出安全风险信息报告；

1、其他要求和车端保持一致

2、特别注意：因为云端涉及到的阶段更多，设计上可能需要在车端的基础

上，想想具体怎么呈现更美观。

3、下端信息检索框：

（1）检索信息顺序及内容包括企业名称、企业类型、处理阶段（下拉列表，

收集、存储、传输、加工、提供、公开、销毁）、风险等级（下拉列表，高风

险、中风险、低风险）、处理状态（下拉列表已处理、处理中、未处理）、风

险等级（下拉列表）、发生时间（可以选择起止时间）；

（2）风险信息表头前后顺序及内容包括企业名称、企业类型、处理阶段、风

险等级、处理状态、风险等级、风险描述、当前状态（已处理、处理中、未处

理）、详细信息（下边内容为查看详情）、风险溯源（下边内容为溯源追踪）

、发生时间、处置完成时间；

（3）点击详细信息后的弹弹框界面，在包含（2）中表头信息的基础上，主要

是对风险信息的详细描述，包括风险危害程度、风险发生可能性、处置建议

等，还需要其他哪些信息和刁工一起商议，点击后详细信息框参照示例样式。

事件管理

车端

1、上端事件统计框：参照风险管理中的样式设计；表达要素包括事件总数、

事件处置状态（已处理、处理中、未处理）、事件分类占比、事件处置率；

2、下端信息检索框：

（1）检索信息顺序及内容包括注册企业、车辆VIN码、车辆品牌、车型、事件

类型（下拉列表）、处理状态（下拉列表已处理、处理中、未处理）、上报时

间；

（2）事件信息表头前后顺序及内容包括注册企业、车辆VIN码、车辆品牌、车

型、事件类型、当前状态（已处理、处理中、未处理）、上报时间、处置完成

时间；

（3）【事件信息导出】：具体逻辑按照通用的报告导出的样式设计

云端

1、其他要求和车端保持一致

2、下端信息检索框：

（1）检索信息顺序及内容包括企业名称、企业类型、事件类型、处理状态

（下拉列表已处理、处理中、未处理）、上报时间；

（2）事件信息表头前后顺序及内容包括企业名称、企业类型、事件类型、当

前状态（已处理、处理中、未处理）、上报时间、处置完成时间；

应急溯源管理

车端应急溯源

政府主管部门能够直观查看安全风险事件列表，列表包括事件编号、单位名称、安全风

险类别、风险详情描述等信息；同时支持对事件关联违规操作的深入追溯，用户可点击

事件查看数据在监管生命周期中的违规环节及具体操作日志，全面掌握安全风险事件的

全貌，为后续整改和执法提供依据；

支持层层点击，深度链接，体现溯源追踪机制。

支持车端的历史轨迹回放；

车端/云端安全风险事件列表：同上

车端安全风险溯源信息：

安全风险事件列表—>违规数据列表【涉及的数据处理活动环节(采集、存储、传输) 、涉及数据类型】—>操作日志记录(见操作信息管理)—>责任主体（包含企业基本信

息）.....(可以继续溯源); 前边的描述只是一个简单的思路，该部分内容具体怎么实现，详

细咨询刁工，根据刁工的思路设计该页面

溯源信息界面中需要根据车端和云端溯源的特点，刁工主导设计，刘工负责实

现，很重要。实时监测的风险管理和应急溯源界面最为关键，田总、刁工要参

与设计。

云端应急溯源 同上

云端安全风险溯源信息：

安全风险事件列表—>违规数据列表【涉及的数据处理活动(收集、存储、传输、加工

使用、提供、公开、销毁、出境、转移、委托处理)、涉及数据类型】—>操作日志记

录(见操作信息管理)—>责任主体（包含企业基本信息）—>支撑单位基本细信息

等.....(可以继续溯源); 操作信息管理 车端操作信息

为政府主管部门提供全面、直观的数据视图，分别显示来自车端操作信息；

支持按照时间、车辆VIN码、企业名称等关键词进行模糊搜索；

支持一键导出操作信息信息报告；

车端操作信息：按照协议字段设置，全量显示

云端操作信息：按照协议字段设置，全量显示

感觉根据需要数据处理阶段去设计数据库结构，具体咨询刁工。

操作信息搜索：车端/云端分别按照对应的关键词进行搜索

1、检索信息的顺序及内容包括注册企业、车辆VIN码、车辆品牌、车型、处理

阶段（下拉列表）、操作时间；

2、操作表头信息：序号、注册企业、车辆VIN码、车辆品牌、车型、处理阶段

、操作时间

3、导出EXCEL:改为【导出操作信息】，具体弹框设计按照标准导出对话框样

式设计

云端操作信息

为政府主管部门提供全面、直观的数据视图，分别显示来自云端等操作信息；

支持按照时间、企业名称等关键词进行模糊搜索；

支持一键导出操作信息信息报告；

1、检索信息的顺序及内容包括企业名称、企业类型、处理阶段（下拉列表，

收集、存储、传输、加工、提供、公开、销毁）、操作时间；

2、操作表头信息：序号、企业名称、企业类型、处理阶段、操作时间；

3、导出EXCEL:改为【导出操作信息】，具体弹框设计按照标准导出对话框样

式设计

监督检查

（先不实现）

检查信息管理 —

为政府主管部门提供全面、直观的数据视图，综合显示已经完成“双随机，一公开”检

查的企业基本信息，同时显示各类企业（资质单位、车企、智驾方案解决商等）的占比

（环形图等）；一次性抽查合格率、包括企业整改完成率、超期任务占比统计等。

企业ID、企业类型、抽查任务编号、问题处理进展等关键词进行监督检查企业信息搜

索；

同一企业历次检查结果横向对比（柱状图+时间线），突出整改成效；

支持一键导出检查信息报告；

顶端统计信息显示：非资质单位***（具体数量）、资质单位**（具体数量）；

显示各类企业占比（环形图等）：非资质单位（车企、智驾方案解决商、其他），资

质单位（甲导、乙导）

已完成双随机、一公开检查的企业基本信息显示：序号、企业名称、企业类型、对

应抽查任务号、抽查任务附件、线下检查时间、线下检查人员、检查结果、问题记录

清单、问题处理进展（完成/进行中/未开展）、企业信用等级（A 优秀 B良好 C警告

D严重违规）

监督检查企业信息搜索：按照各个企业基本信息单独设置搜索条件

企业信用评估模型：整合备案合规率（30%）+风险发生率

（40%）+整改及时性（30%）动态生成信用等级，影响抽查概

率；

采用通用的评估模型即可。

检查任务管理

我的门户

信息发布：可以调用模版发布相关政策、标准、公告等通知，向企业端账户同步信息。

（该功能放在此处是否合适，还是放在系统管理中合适）

需要考虑通知的发布是否需要内部领导审批？？？

上链存证（企业端电子签名等），防止对生成的任务信息篡改；

企业及检查人员信息增删改查对应模版参照【检查任务生成】；

待办任务：集中显示与监督检查相关的待办任务列表，为政府主管部门提供高效、便捷

的监督检查任务处理功能，支持对各类申请集中管理和审批，可以点击查看每条申请的

详细信息；审批结果同步反馈给企业用户。

申请列表：序号、企业名称类型（抽查任务公示/检查问题下发/检查结果公示）、标

题、提交时间、当前待办人；

抽查任务公示申请模版-政府端：基本信息（申请人、申请日

期）、任务信息（抽查企业列表：序号、抽查单位名称、企业性

质-车企/资质单位/智驾方案解决商、资质情况-无/甲导/乙导）

、检查人员信息（序号、姓名、单位名称、职务、资质证书编号

、证书截止期限）、检查任务公文（附件导入）、领导审批意见

（领导审批意见备注批注框）

检查问题下发申请模版-政府端：存在问题（结构化问题勾选-参

照注册备案阶阶段、附件-现场检查记录问题清单及现场存证）

、截止整改期限等。

检查结果公示申请模版-政府端：基本信息（申请人、申请日

期）、任务信息（抽查企业列表：序号、抽查单位名称、企业性

质-车企/资质单位/智驾方案解决商、资质情况-无/甲导/乙导、

检查结果-合格/不合格，问题清单-现场检查记录问题清单及现

场存证、检查日期）、检查人员信息（序号、姓名、单位名称、

职务、资质证书编号）、检查公示公文（附件导入）、领导审批

意见（领导审批意见备注批注框）；需要同步推送到指定网站；

检查任务整改-企业端：集中显示企业问题整改进度列表，并对

企业整改问题的措施进行记录上传，并对问题整改结果进行最终

审批；（具体参照企业端备案申请模版）

不可篡改机制：

抽查任务参数生成后立即计算SHA-256哈希值上链；

检查人员现场取证照片自动添加数字水印；

已办任务：显示已经完成的任务列表，可以点击查看已经完成的任务信息。

任务列表：序号、类型（抽查任务公示/检查问题下发/检查结果公示/检查问题整

改）、标题、提交时间、当前待办人；

审批记录：序号、节点名称、电子签名、签名日期、操作、留言

发起跟踪：显示发起的任务审批列表，可以点击查看的当前的任务审批流程

任务列表：序号、类型（抽查任务公示/检查问题下发/检查结果公示/检查问题整

改）、标题、提交时间、当前待办人；

审批记录：序号、节点名称、电子签名、签名日期、操作、留言

检查任务生成

针对事后监督检查职责，整合抽查对象库（企业）和检查人员库，利用智能随机算法

（高风险企业抽查概率提升50%），一键生成“双随机、一公开”检查任务，自动生成公

示文件（需要包含生成日期、单位名称及公章等重要细信息），完成审批后自动下发至

所有企业单位。

企业信息列表：序号、企业名称、企业性质、抽查次数

检查人员列表：序号、姓名、单位名称、职务、资质证书编号、证书截止期限、已完

成抽查次数

生成按钮：对应任务池显示生成的结果，并记录操作员及生成日期

企业及检查人员库编辑：支持对企业信息列表和检查人员列表的增删改查。

区块链存证

1. 抽查算法参数上链

2. 企业电子签名采用SM2国密算法

3. 公示页面嵌入存证二维码

系统管理

用户管理 — 对用户信息进行管理，包括查询、新增、密码重置、编辑、删除、批量操作等。 通用功能

这部分都是通用功能了，找刁工审核（结合业务方案和之前和孟工梳理的角色

表审核），作进一步调整和优化

角色管理 — 对角色信息进行管理，包括查询、新增、权限配置、编辑、删除等。 通用功能

超级管理员：全系统权限，可操作所有模块（含用户/角色管

理）

安全监测员：实时监控+备案审核+监督检查权限，可操作综合概

览/监测预警/应急溯源

审核专员：备案审核全流程权限，可操作申请审核/申诉处理/信

息管理

监督检查员：双随机抽查全周期权限，任务生成/过程记录/整改

跟踪/结果公示

数据分析员：只读权限（数据统计+导出）权限，所有模块的统

计图表和报告功能

车辆管理 —

实现对注册备案车辆的综合管理，包含车牌号、车型、车辆用途、应用场景、VIN、接入

时间、所属企业、传感系统配置、车辆外观照片等进行管理，可对车辆进行新增车辆、

修改车辆信息、删除车辆、查询车辆信息等操作。

通用功能 要注意区分量产车和研采车

企业管理 — 实现对注册备案企业的查询、新增、修改、删除、批量操作等。 通用功能

区域管理 —

根据主管部门更新的测试区域，进行电子信息围栏区域修正，提供预览功能，并同步至

实时监控的显示界面（支持坐标输入、框选区域等方式）；

地理信息围栏版本管理（支持历史围栏对比）；

更新测试区域范围（注意是设置区域）：完成经度、纬度或区域设置，完成预览、更

新发布；

记录测试区域历史更新信息：序号、测试区域范围、更新时间、操作人员；

地理信息围栏历史版本比对；

日志管理 —

备案审核阶段：所有账户登录和操作（审批、申诉处理、派单、一键导出等）均记录日

志，支持溯源定责；

实时监测阶段：所有账户登录和操作（查询、一键导出等）均记录日志，支持溯源定

责；

监督检查阶段：所有账户登录和操作（任务生成、审批、派单、公示等）均记录日志，

支持溯源定责；

系统管理阶段：其他需要记录的操作日志（如操作权限变更等）。

通用功能

日志管理要与记录的所有业务数据相关联，实现全生命周期溯

源；

日志信息定期上链，防止篡改；

日志留存时间不少于1年，关键日志留存不少于3年；

时空数据安全监测平台系统功能汇总 （企业端）

注：

模块分为登录 、我的门户 、风险统计 、实时监控 、监督检查 、系统管理 ，通过顶端导航栏区分 ；

二级子模块通过每个导航栏左侧的功能栏区分 ，左侧功能栏随顶部导航切换动态切换 ；

三级子模块设置左侧的功能栏二级子模块的下一级 （可能没有那么多功能 ，视情况而定 ）；

具体导航栏和功能栏的可见 、可编辑配置 ，可以通过账户的权限管理进行配置 （涉及到角色权限问题需要考虑 ）； 模块 二级子模块 功能描述 关键字段信息 备注 修改建议 示例

登陆

注册

这个注册应该是登录

前的界面 ，填写注册

信息，进行账号注册

企业基本信息

企业名称 （需与营业执照一致 ）

统一社会信用代码

注册地址 /经营地址

企业类型 □ 整车生产企业 □ 平台运营商 □ 智驾方案提供商 □ 地图服务商 □其他________ 成立日期

注册资本

法人代表信息 （姓名、联系方式 、身份证号 等）

营业执照扫描件

下一步（保存当前页面信息并跳转到下个页面 ）

测绘资质信息 （无相关资质可空 ）

资质类别与等级 （分甲乙等级 ，需要做成展示平铺的方式 ，资质类型可以多选 ）：互联网地图 、导航电子地图制作 、地理信息系统工程

资质证书编号

发证机关

资质有效期

资质状态

资质证书扫描件

特殊情况说明

数据安全防控措施 -时空数据安全管理制度

组织架构 ：

数据安全负责部门 、数据安全负责人姓名 、数据安全负责人职位

数据安全组织架构管理制度 （上传附件形式 ）

数据分类分级管理 ：

数据分类分级管理制度 （上传附件形式 ）

重要核心数据目录 （可选项、若存在则上传 ，上传附件形式 ）---增加注释内容 “上报内容发生重大变化的 ，数据处理者应在发生变化的 3个月内履行

变更手续 ，重大变化是指数据内容发生变化导致原有级别不再适用的 ，或某类重要数据规模变化 30%以上的等”

制度体系 ：

登陆

输入用户名 、账号检

验登陆，登录时企业

账号登录要与政府端

账号登录要有一些区

分

用户名、密码、校验码

找回密码

忘记密码后可通过邮

箱、电话、身份证等

找回

综合概览

(首页/我的门户) 左上：我的填报

包含注册信息填报 、数

据目录填报 、风险评估

填报,每个下边增加模

版，供操作者调用

点解之后的显示界面 ，需要结合具体的类型进行详细设计 。

填报反馈 ：序号、类别、标题、状态（成功、失败）、接收时间 ，点击后的详细信息界面中还需包括申请人 、申请时间、失败原因、截止填报时间 、

注册填报时填写的所有信息 （看是否合理 、若合理导致注册失败的地方需要标记 ）

风险反馈 ：序号、类别、标题、接收时间 、点击后的详细信息界面还需要包括风险产生时间 、风险等级、风险详情、处置建议、当前状态、截止处置

日期、车端风险 （还应具体包含告警时间 、车辆VIN码、数据处理阶段 、车辆品牌 、车辆型号）

最底下应该设计处理 、申诉等点击按钮进行相关功能的跳转

1、注册信息填报 ：点击进入界面之后 ，弹出【注册信息管理

】界面，其实分为两种情况 ，1种是完全空白 ，即首次填报 ，

此时需要有 【信息填报 】按钮，触发信息填报流程 ，弹出的

界面其实就是政府端 I20单元格内容中梳理的 5个分类内容 ，

把之前的迁移过来作为 ，内容全部为空就行 ；需要注意的是

也要做成TAB页的形式，每个tab页下边应该有 【保存】、【

上一页】【下一页】按钮，支持切换和保存 ；每保存一页 ，

该信息就应该暂存在 【注册信息管理 】页面中，避免丢失重

复上传；

2、其他数据目录填报 、数据目录填报 、风险评估填报 、风险

事件处置，未来就是加在模版填报 ，后续设计待实现 ；

3、界面样式再调整一下 ，边框圆润程度 、阴影样式、渐变色

填充都需要优化 ；

4、待办任务：表头内容及顺序为序号 、类别（注册反馈、风

险提醒、风险处置 、事件处置 ）、标题（用于简单描述待办

任务内容）、操作（查看详情 ）

5、注册反馈：点击详情的调准逻辑不对 ，点击后的详细信息

界面中应包括类别 、标题、申请人、申请时间 、失败原因 、

截止填报时间 、查看填报信息 （跳转到修改后的注册填报信

息界面，并标记出导致注册失败的内容 ）；注意将同类的内

容放到一个整体框内显示 。

6、风险提醒：点击详情的调准逻辑不对 ，点击后的详细信息

界面中应包括类别 、标题、风险产生时间 、风险类别 （车端/ 云端）、风险所处阶段 （收集、存储、传输、加工、提供、

公开、销毁）、风险等级 （高中低）、风险描述 、处置建议

、当前状态、截止处置日期 、若是车端风险 （还应具体包含

车辆VIN码、车辆品牌、车辆型号等信息 ）；下边设计【确认

风险】（点击弹出信息确认对话框 ）、【申诉风险 】（支持

对该风险进行申诉处理 ，对话框中的内容应包括风险 ID、类

右上：通知

主要同于显示政策法规

动态信息 ，最上边显示

最新的消息 ，消息最前

边需要体现消息的分

类，类别可以包括通用

的法规、政策、公告、

通用信息每个企业端账

号都会收到 ；

下边：所有任务

用于显示所有任务信

息，可以分为多个 tab 页显示如待办任务 、已

办任务、待阅任务 、已

阅任务

每个任务下需要包含

的字段信息包括 ：序

号、类别（填报反馈

、风险反馈、检查通

知）、标题、接收时

间等。

风险统计

左上：风险级别统计

点解每个通知 ，都可

以查看该通知的详细

信息。

需要区分车端 /云端的安全风险 ，通过按钮点击实现切换 ，包含高/中/低 这里边需要内置一些过滤统计

的内容，比如可以顾虑显示已

处理/处理中/未处理的风险 ，

每个流程中也可以再过滤显示

每个级别 、每个数据处理阶段

的风险信息

1、整体导航栏的名称改为 【风险事件】，左边区分两个功能

栏，一个是风险统计 ，另外一个是事件统计 ；

2、风险统计/事件统计可以参照政府端风险的样式设计 ，界

面风格可以完全一样 ；

3、布局参考（也可以自己设计 ）：左侧1/3绘制统计信息的

显示，右侧2/3写逐条呈现的风险或事件信息 ，注意风险统计

中车端风险和云端风险做成 TAB页的形式，不再是既有的这种

平铺展开样式 ，事件统计作相同设计 ；

4、风险统计要展示的内容 ：风险统计内容与政府端风险统计

的内容相同 ，注意是要包含车端和云端所有的风险 ，具体是

直观展示还是作成 TAB切换的形式 ，需要详细设计这个界面 ；

5、事件统计要展示的内容 ：事件统计内容与政府端事件统计

的内容相同 ，注意是要包含车端和云端所有的事件 ，具体是

直观展示还是作成 TAB切换的形式 ，需要详细设计这个界面 ；

6、风险信息统计表中的内容参照政府端中的内容设计 ，点击

【操作】中的查看详情 ，弹框的内容设计同样参照政府端 ，

基本上内容都是一致大的 ；

7、事件信息统计表中的内容参照政府端中的内容设计 ，点击

【操作】中的查看详情 ，弹框的内容设计同样参照政府端 ，

内容基本上时一致的 ；

8、

这部分内容刁工和田总要参与设计

左下：风险项统计

显示月度 /年度各类

风险出现频次 ，风险

以二级目录为项 ，以

柱状图显示 ，右上角

支持切换年度 /月度/ 日度统计

需要区分车端 /云端的安全风险 ，通过按钮点击实现切换 ；按照数据处理阶段统计风险 ，车端包括收集阶段 、存储阶段和传输阶段 ；云端包括收集 、存

储、加工、传输、提供、公开、销毁阶段 ；

右边：风险详情 （可以

分上、下区分车端和云

端）

分车端和云端 ，显示

风险统计信息 。

车端风险 ：包含告警时间 、车辆VIN码、数据处理阶段 、车辆品牌 、车辆型号 、风险等级、当前状态（超时未处理 、待处理、处理中、已处理）

云端风险 ：告警时间 、数据处理阶段 、风险等级 、当前状态

1、支持对应各个字段进行模

糊查询；

2、点解每条风险可以查看该

风险的详细信息 ，详细信息除

了E列中需要包含的字段外 ，

还需要包含风险详细信息 （简

要概括风险的信息 ），对应处

置建议，对于待处置的风险要

注明处理截止日期

3、需要有相关的点击按钮支

持跳转处置 、申诉等功能界面

注册信息管理

企业基本信息 见注册所填信息

因为这些信息都可能会涉及到

后期高频次的更新 ，但是每次

更新可能只涉及部分信息的更

新，因此对注册信息以及后溪

需要更新的信息作了分类 ，这

里边需要考虑未来更新的时候

如何编辑信息 ，或是如何跳转

实现更新填报的功能

1、这部分内容其实就是对自己当时注册内容的查看 、更新功

能，直接展开内容就行 ，要支持注册信息的更新跳转 ，并记

录每次注册更新的记录 ；

2、需要正价过滤框 ，内容包括注册状态 （成功/失败/审核

中）、注册人、注册时间 （支持选择起止时间 ）；

3、表格的表头顺序及内容为 ：序号、企业名称、企业类型企

业基本信息 （成功/失败/审核中）、测绘资质信息 （成功/失

败/审核中）、车辆信息（成功/失败/审核中）、安全防护措

施（成功/失败/审核中）、处理活动信息 （成功/失败/审核

中）、注册时间 、操作（查看详情 ）

4、点击查看详情后的界面 ：5大类基本信息也要参照政府端

（I20单元格）的界面展开设计 ，把审批记录也迁移过来 ；增

加【下一页】【上一页】，实现上下页切换 ；增加【更新】

按照，支持跳转更新页面 （这个页面用户要有保存和提交按

钮，支持重新填报 ）；增加【申诉】按钮，这里的逻辑都参

照H7单元格中的内容设计 。

测绘资质信息 同上

安全防控措施 同上

数据处理活动信息 同上

数据基本信息

其实和数据目录是同

步的，一是看这部分

是否有必要体现 ，二

是若需要体现以什么

形式呈现 ，是单独实

现还是界面跳转 ？

地方活动申请

（暂时先不实现 ）

地方活动申请

活动基本信息

活动类型 ：测试类

（技术验证、功能测

试）、运营类（商业

服务、用户数据收

集）、科研类

时间范围 ：起止日期

、每日运行时段

参与人员权限清单 ：

姓名、角色、操作范

围

参与设备清单

第三方合作方数据使

用协议

应急相应计划 ：数据

泄漏处置流程 、备用

设备切换预案

责任承诺书 ：遵守地

方性法规 、数据活动

负责人签名

附件

必要：活动技术方案

、保险凭证

可选：第三方安全评

估报告

（关联信誉分 ：若发

现超限操作 ，形成整

改工单并扣减企业信

誉分

实时监测

（日志管理 ）

（暂时先不实现 ）

实时数据流面板

动态地图 ：显示在线

/离线车辆末次上传

数据的位置 （不需要

每辆车显示 ，每一小

块区域包含车辆数即

可）

数据流量监控 ：显示

单位时间内接受的日

志量及传输成功率

日志管理 支持日志检索及导出

筛选条件 ：时间范围 、车辆/设备ID、操作类型 （传输、删除、采集）、导出管理（格式JSON/CSV/PDF ）

原始数据导出申请 （政府端审批 ）

智能检测报告

自动生成年度 /月度

风险趋势报告 ，并列

出相应分析及处理进

展

合规存证

确保日志可追溯 、防

篡改，满足监管要求

操作留痕 ：显示日志查询 、导出、删除等操作 （操作人+时间戳）

访问权限审批 （仅企业管理员账号 ）

访问权限申请 （非企业管理员账号 ）

风险处置（该部分

内容整合到 “我的

门户”界面实现 ）

工单处理

接收政府下发的风险

处置工单

显示详细风险内容 、涉及车辆等

上传修复证明等

案例库

查看同类风险处置方

案及政府评价反馈查

询

培训资源 （先不实现 ）

在线课程库

对企业数据操作人员

进行培训的课程资源

政策解读

时空数据相关政策解

读文件汇总

系统管理

用户管理

对账户信息进行管

理，包括查询、新增

、密码重置、编辑、

删除、批量操作等 。

字段包括 ：序号、姓名、角色（超级管理员 、注册信息管理专员 、风险处置专员 、系统维护专员等 ）、创建日期、对应权限（可以以顶端导航栏的功

能区分来定义 ）等

这部分实际为通用功能 ，需要

结合之前类似的项目经验 ，系

统涉及该部分功能 ，细化到每

个字段

这部分都是通用功能了 ，找刁工审核 （结合业务方案和之前

和孟工梳理的角色表审核 ），作进一步调整和优化 。

角色管理

对角色信息进行管

理，角色：管理员、

审计员、操作员并设

置相关权限

管理员：全部企业账号权限 ，并可为下属账号分配权限

安全监测员 ：实时监控 +备案审核 +监督检查权限 ，可操作综合概览 /监测预警 /应急溯源

审核专员 ：备案审核全流程权限 ，可操作申请审核 /申诉处理 /信息管理

数据分析员 ：只读权限 （数据统计 +导出）权限，所有模块的统计图表和报告功能

要求同上

车辆管理 （该部分内容

在注册信息管理中体现

并实现相关功能 ）

实现对注册备案车辆

的综合管理 ，包含车

牌号、车型、车辆用

途、应用场景、VIN 、接入时间、所属企

业、传感系统配置 、

车辆外观照片等进行

管理，可对车辆进行

新增车辆 、修改车辆

信息、删除车辆、查

询车辆信息等操作 。

支持一键导入

区域管理

根据主管部门更新的

测试区域 ，进行电子

信息围栏区域修正 ，

提供预览功能 ，并同

步至实时监控的显示

界面（支持坐标输入

、框选区域等方式 ）

更新测试区域范围 （注意是设置区域 ）：完成经度 、纬度或区域设置 ，完成预览 、更新发布

记录测试区域历史更新信息 ：序号、测试区域范围 、更新时间 、操作人员

车端 顶端统计框 云端

车辆信息

地图显示

企业信息

处理活动（日月年） 处理活动（日月年）

风险事件（日月年）

（车端）风险预警信息 （云端）风险预警信息

风险事件（日月年）

高中低风

险统计信

息

事件信息统

计

高中低风

险统计信

息

事件信息统

计

测绘资质信息

序号 资质类别 资质等级 证书编号 资质有效期 资质状态 资质证书附件

互联网地图资质 甲级/乙级/甲、乙级 1111 2023.10.1-2025.11.1 有效/过期

导航电子地图资质 甲级/乙级 111

地理信息系统工程 甲级/乙级

特殊情况说明：

注：未来有那条资质就显示哪条就可以了

地理信息安全监测平台 首页 注册管理 实时监测 系统管理

风险管理

车端风险

云端风险

事件管理

车端事件

云端事件

应急溯源管理

车端溯源

云端溯源

操作信息管理

车端操作信息

云端操作信息

