{"version": 3, "sources": ["../../../src/components/tooltip.props.tsx"], "sourcesContent": ["import { widthPropDefs } from '../props/width.props.js';\n\nimport type { PropDef, GetPropDefTypes } from '../props/prop-def.js';\n\nconst tooltipPropDefs = {\n  content: { type: 'ReactNode', required: true },\n  width: widthPropDefs.width,\n  minWidth: widthPropDefs.minWidth,\n  maxWidth: { ...widthPropDefs.maxWidth, default: '360px' },\n} satisfies {\n  width: PropDef<string>;\n  minWidth: PropDef<string>;\n  maxWidth: PropDef<string>;\n  content: PropDef<React.ReactNode>;\n};\n\ntype TooltipOwnProps = GetPropDefTypes<typeof tooltipPropDefs & typeof widthPropDefs>;\n\nexport { tooltipPropDefs };\nexport type { TooltipOwnProps };\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,qBAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA8B,mCAI9B,MAAMF,EAAkB,CACtB,QAAS,CAAE,KAAM,YAAa,SAAU,EAAK,EAC7C,MAAO,gBAAc,MACrB,SAAU,gBAAc,SACxB,SAAU,CAAE,GAAG,gBAAc,SAAU,QAAS,OAAQ,CAC1D", "names": ["tooltip_props_exports", "__export", "tooltipPropDefs", "__toCommonJS", "import_width_props"]}