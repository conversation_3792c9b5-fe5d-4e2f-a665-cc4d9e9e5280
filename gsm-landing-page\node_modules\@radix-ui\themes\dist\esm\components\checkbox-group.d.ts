import * as React from 'react';
import * as CheckboxGroupPrimitive from './checkbox-group.primitive.js';
import { checkboxGroupRootPropDefs } from './checkbox-group.props.js';
import type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';
import type { MarginProps } from '../props/margin.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
type CheckboxGroupRootOwnProps = GetPropDefTypes<typeof checkboxGroupRootPropDefs>;
interface CheckboxGroupRootProps extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>, MarginProps, CheckboxGroupRootOwnProps {
}
declare const CheckboxGroupRoot: React.ForwardRefExoticComponent<CheckboxGroupRootProps & React.RefAttributes<HTMLDivElement>>;
interface CheckboxGroupItemProps extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Item, RemovedProps>, MarginProps {
}
declare const CheckboxGroupItem: React.ForwardRefExoticComponent<CheckboxGroupItemProps & React.RefAttributes<HTMLButtonElement>>;
export { CheckboxGroupRoot as Root, CheckboxGroupItem as Item };
export type { CheckboxGroupRootProps as RootProps, CheckboxGroupItemProps as ItemProps };
//# sourceMappingURL=checkbox-group.d.ts.map