{"name": "vite-hot-client", "type": "module", "version": "2.1.0", "description": "Get Vite's import.meta.hot at runtime.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-hot-client#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/vite-hot-client.git"}, "bugs": {"url": "https://github.com/antfu/vite-hot-client/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "peerDependencies": {"vite": "^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@antfu/ni": "^25.0.0", "@babel/types": "^7.27.7", "@types/node": "^24.0.7", "bumpp": "^10.2.0", "eslint": "^9.30.0", "pnpm": "^10.12.4", "rimraf": "^6.0.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vite": "^7.0.0", "vitest": "^3.2.4"}, "scripts": {"build": "rimraf dist && unbuild && tsx scripts/patch-types.ts", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "typecheck": "tsc --noEmit"}}